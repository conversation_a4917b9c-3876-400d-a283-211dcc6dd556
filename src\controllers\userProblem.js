const {getLanguageById,submitBatch,submitToken} = require("../utils/problemUtility");
const Problem = require("../models/problem");
const User = require("../models/user");
const Submission = require("../models/submission");
const { validateProblemData } = require('../utils/validation');
const { NotFoundError, ValidationError, Judge0Error } = require('../utils/errorHandler');
const { HTTP_STATUS, JUDGE0_STATUS } = require('../utils/constants');
const logger = require('../utils/logger');

const createProblem = async (req,res)=>{

  // API request to authenticate user:
    const {title,description,difficulty,tags,
        visibleTestCases,hiddenTestCases,startCode,
        referenceSolution, problemCreator
    } = req.body;

    // Validate required fields
    if (!title || !description || !difficulty || !tags) {
      return res.status(400).send("Missing required fields: title, description, difficulty, or tags");
    }

    if (!visibleTestCases || !Array.isArray(visibleTestCases) || visibleTestCases.length === 0) {
      return res.status(400).send("At least one visible test case is required");
    }

    if (!hiddenTestCases || !Array.isArray(hiddenTestCases) || hiddenTestCases.length === 0) {
      return res.status(400).send("At least one hidden test case is required");
    }

    if (!startCode || !Array.isArray(startCode) || startCode.length !== 3) {
      return res.status(400).send("Start code for all three languages (c++, java, javascript) is required");
    }

    if (!referenceSolution || !Array.isArray(referenceSolution) || referenceSolution.length !== 3) {
      return res.status(400).send("Reference solution for all three languages (c++, java, javascript) is required");
    }

    try{

      // Optional: Skip reference solution validation during development
      const skipValidation = process.env.SKIP_REFERENCE_VALIDATION === 'true';

      if (!skipValidation) {
        for(const {language,completeCode} of referenceSolution){
         

        // source_code:
        // language_id:
        // stdin: 
        // expectedOutput:

        const languageId = getLanguageById(language);
          
        // I am creating Batch submission
        const submissions = visibleTestCases.map((testcase)=>({
            source_code:completeCode,
            language_id: languageId,
            stdin: testcase.input,
            expected_output: testcase.output
        }));


        const submitResult = await submitBatch(submissions);
        // console.log(submitResult);

        const resultToken = submitResult.map((value)=> value.token);

        // ["db54881d-bcf5-4c7b-a2e3-d33fe7e25de7","ecc52a9b-ea80-4a00-ad50-4ab6cc3bb2a1","1b35ec3b-5776-48ef-b646-d5522bdeb2cc"]
        
       const testResult = await submitToken(resultToken);


       console.log('Test results for', language, ':', testResult);

       for(const test of testResult){
        if(test.status_id!=3){
         console.error(`Test failed for ${language}:`, test);
         return res.status(400).send(`Reference solution validation failed for ${language}. Status: ${test.status_id}, Error: ${test.stderr || test.compile_output || 'Unknown error'}`);
        }
       }

      }
      } // Close the skipValidation if block


      // We can store it in our DB

    const userProblem =  await Problem.create({
        ...req.body,
        problemCreator: req.result._id
      });

      res.status(201).send("Problem Saved Successfully");
    }
    catch(err){
        console.error('Error creating problem:', err);

        // Handle specific mongoose validation errors
        if (err.name === 'ValidationError') {
          const validationErrors = Object.values(err.errors).map(e => e.message);
          return res.status(400).send(`Validation Error: ${validationErrors.join(', ')}`);
        }

        // Handle Judge0 API errors
        if (err.message && err.message.includes('Judge0')) {
          return res.status(500).send(`Judge0 API Error: ${err.message}`);
        }

        res.status(400).send("Error: "+err.message || err);
    }
}

/**
 * Update an existing problem
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateProblem = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      difficulty,
      tags,
      visibleTestCases,
      hiddenTestCases,
      startCode,
      referenceSolution,
      problemCreator
    } = req.body;

    logger.info('Problem update request', { problemId: id, userId: req.result._id });

    // Validate required fields
    if (!id) {
      throw new ValidationError('Problem ID is required');
    }

    // Check if problem exists
    const existingProblem = await Problem.findById(id);
    if (!existingProblem) {
      throw new NotFoundError('Problem');
    }

    // Validate problem data if provided
    if (req.body.title || req.body.description || req.body.visibleTestCases || req.body.referenceSolution) {
      validateProblemData(req.body);
    }

    // Test reference solutions against visible test cases if both are provided
    if (referenceSolution && visibleTestCases && !process.env.SKIP_REFERENCE_VALIDATION) {
      logger.debug('Testing reference solutions', {
        solutionCount: referenceSolution.length,
        testCaseCount: visibleTestCases.length
      });

      for (const { language, completeCode } of referenceSolution) {
        try {
          const languageId = getLanguageById(language);

          const submissions = visibleTestCases.map((testcase) => ({
            source_code: completeCode,
            language_id: languageId,
            stdin: testcase.input,
            expected_output: testcase.output
          }));

          const submitResult = await submitBatch(submissions);
          const resultTokens = submitResult.map((result) => result.token);
          const testResults = await submitToken(resultTokens);

          // Check if all test cases pass
          const failedTests = testResults.filter(test => test.status_id !== JUDGE0_STATUS.ACCEPTED);
          if (failedTests.length > 0) {
            logger.error('Reference solution validation failed', {
              language,
              failedTests: failedTests.map(test => ({
                status_id: test.status_id,
                stderr: test.stderr,
                stdout: test.stdout
              }))
            });

            throw new ValidationError(
              `Reference solution for ${language} failed ${failedTests.length} test case(s). Please verify your solution.`
            );
          }

          logger.debug('Reference solution validated successfully', { language });
        } catch (error) {
          if (error instanceof ValidationError) {
            throw error;
          }
          logger.error('Error validating reference solution', {
            language,
            error: error.message
          });
          throw new ValidationError(`Failed to validate ${language} reference solution: ${error.message}`);
        }
      }
    }

    // Update the problem
    const updatedProblem = await Problem.findByIdAndUpdate(
      id,
      { ...req.body },
      { runValidators: true, new: true }
    );

    logger.info('Problem updated successfully', {
      problemId: id,
      title: updatedProblem.title,
      updatedBy: req.result._id
    });

    res.status(HTTP_STATUS.OK).json({
      success: true,
      message: 'Problem updated successfully',
      problem: updatedProblem
    });

  } catch (error) {
    logger.error('Error updating problem', {
      problemId: req.params.id,
      error: error.message,
      stack: error.stack,
      userId: req.result?._id
    });

    if (error instanceof ValidationError || error instanceof NotFoundError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message
      });
    }

    if (error instanceof Judge0Error) {
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Code validation service temporarily unavailable'
      });
    }

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

const deleteProblem = async(req,res)=>{

  const {id} = req.params;
  try{
     
    if(!id)
      return res.status(400).send("ID is Missing");

   const deletedProblem = await Problem.findByIdAndDelete(id);

   if(!deletedProblem)
    return res.status(404).send("Problem is Missing");


   res.status(200).send("Successfully Deleted");
  }
  catch(err){
     
    res.status(500).send("Error: "+err);
  }
}


const getProblemById = async(req,res)=>{

  const {id} = req.params;
  try{
     
    if(!id)
      return res.status(400).send("ID is Missing");

    const getProblem = await Problem.findById(id).select('_id title description difficulty tags visibleTestCases startCode referenceSolution ');
   
   if(!getProblem)
    return res.status(404).send("Problem is Missing");


   res.status(200).send(getProblem);
  }
  catch(err){
    res.status(500).send("Error: "+err);
  }
}

const getAllProblem = async(req,res)=>{

  try{
     
    const getProblem = await Problem.find({}).select('_id title difficulty tags');

   if(getProblem.length==0)
    return res.status(404).send("Problem is Missing");


   res.status(200).send(getProblem);
  }
  catch(err){
    res.status(500).send("Error: "+err);
  }
}


const solvedAllProblembyUser =  async(req,res)=>{
   
    try{
       
      const userId = req.result._id;

      const user =  await User.findById(userId).populate({
        path:"problemSolved",
        select:"_id title difficulty tags"
      });
      
      res.status(200).send(user.problemSolved);

    }
    catch(err){
      res.status(500).send("Server Error");
    }
}

const submittedProblem = async(req,res)=>{
  try{
    const userId = req.result._id;
    const problemId = req.params.pid;

    const ans = await Submission.find({userId,problemId});

    if(ans.length == 0) {
      return res.status(200).send("No Submission is persent");
    }

    res.status(200).send(ans);

  } catch(err){
    console.error('Error in submittedProblem:', err);
    res.status(500).send("Internal Server Error");
  }
}



module.exports = {createProblem,updateProblem,deleteProblem,getProblemById,getAllProblem,solvedAllProblembyUser,submittedProblem};


