const Problem = require("../models/problem");
const Submission = require("../models/submission");
const User = require("../models/user");
const { getLanguageById, submitBatch, submitToken } = require("../utils/problemUtility");
const { validateSubmissionData } = require('../utils/validation');
const { NotFoundError, ValidationError, Judge0Error } = require('../utils/errorHandler');
const { HTTP_STATUS, JUDGE0_STATUS } = require('../utils/constants');
const logger = require('../utils/logger');

/**
 * Get human-readable status message for Judge0 status ID
 * @param {number} statusId - Judge0 status ID
 * @returns {string} Human-readable status message
 */
const getStatusMessage = (statusId) => {
  const statusMap = {
    [JUDGE0_STATUS.IN_QUEUE]: 'In Queue',
    [JUDGE0_STATUS.PROCESSING]: 'Processing',
    [JUDGE0_STATUS.ACCEPTED]: 'Accepted',
    [JUDGE0_STATUS.WRONG_ANSWER]: 'Wrong Answer',
    [JUDGE0_STATUS.TIME_LIMIT_EXCEEDED]: 'Time Limit Exceeded',
    [JUDGE0_STATUS.COMPILATION_ERROR]: 'Compilation Error',
    [JUDGE0_STATUS.RUNTIME_ERROR_SIGSEGV]: 'Runtime Error (SIGSEGV)',
    [JUDGE0_STATUS.RUNTIME_ERROR_SIGXFSZ]: 'Runtime Error (SIGXFSZ)',
    [JUDGE0_STATUS.RUNTIME_ERROR_SIGFPE]: 'Runtime Error (SIGFPE)',
    [JUDGE0_STATUS.RUNTIME_ERROR_SIGABRT]: 'Runtime Error (SIGABRT)',
    [JUDGE0_STATUS.RUNTIME_ERROR_NZEC]: 'Runtime Error (NZEC)',
    [JUDGE0_STATUS.RUNTIME_ERROR_OTHER]: 'Runtime Error',
    [JUDGE0_STATUS.INTERNAL_ERROR]: 'Internal Error',
    [JUDGE0_STATUS.EXEC_FORMAT_ERROR]: 'Exec Format Error'
  };

  return statusMap[statusId] || `Unknown Status (${statusId})`;
};

const submitCode = async (req,res)=>{
   
    // 
    try{
      
       const userId = req.result._id;
       const problemId = req.params.id;

       let {code,language} = req.body;

      if(!userId||!code||!problemId||!language)
        return res.status(400).send("Some field missing");
      

      if(language==='cpp')
        language='c++'
      
      console.log(language);
      
    //    Fetch the problem from database
       const problem =  await Problem.findById(problemId);
    //    testcases(Hidden)
    
    //   Kya apne submission store kar du pehle....
    const submittedResult = await Submission.create({
          userId,
          problemId,
          code,
          language,
          status:'pending',
          testCasesTotal:problem.hiddenTestCases.length
     })

    //    Judge0 code ko submit karna hai
    
    const languageId = getLanguageById(language);
   
    const submissions = problem.hiddenTestCases.map((testcase)=>({
        source_code:code,
        language_id: languageId,
        stdin: testcase.input,
        expected_output: testcase.output
    }));

    
    const submitResult = await submitBatch(submissions);
    
    const resultToken = submitResult.map((value)=> value.token);

    const testResult = await submitToken(resultToken);
    

    // submittedResult ko update karo
    let testCasesPassed = 0;
    let runtime = 0;
    let memory = 0;
    let status = 'accepted';
    let errorMessage = null;


    for(const test of testResult){
        if(test.status_id==3){
           testCasesPassed++;
           runtime = runtime+parseFloat(test.time)
           memory = Math.max(memory,test.memory);
        }else{
          if(test.status_id==4){
            status = 'error'
            errorMessage = test.stderr
          }
          else{
            status = 'wrong'
            errorMessage = test.stderr
          }
        }
    }


    // Store the result in Database in Submission
    submittedResult.status   = status;
    submittedResult.testCasesPassed = testCasesPassed;
    submittedResult.errorMessage = errorMessage;
    submittedResult.runtime = runtime;
    submittedResult.memory = memory;

    await submittedResult.save();
    
    // ProblemId ko insert karenge userSchema ke problemSolved mein if it is not persent there.
    
    // req.result == user Information

    if(!req.result.problemSolved.includes(problemId)){
      req.result.problemSolved.push(problemId);
      await req.result.save();
    }
    
    const accepted = (status == 'accepted')
    res.status(201).json({
      accepted,
      totalTestCases: submittedResult.testCasesTotal,
      passedTestCases: testCasesPassed,
      runtime,
      memory
    });
       
    }
    catch(err){
      res.status(500).send("Internal Server Error "+ err);
    }
}


/**
 * Run code against visible test cases
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const runCode = async (req, res) => {
  try {
    const userId = req.result._id;
    const problemId = req.params.id;
    let { code, language } = req.body;

    logger.info('Code run request', { userId, problemId, language });

    // Validate required fields
    if (!userId) {
      throw new ValidationError('User authentication required');
    }
    if (!problemId) {
      throw new ValidationError('Problem ID is required');
    }

    // Validate submission data
    validateSubmissionData({ code, language });

    // Normalize language (handle legacy 'cpp' format)
    if (language === 'cpp') {
      language = 'c++';
    }

    // Fetch the problem from database
    const problem = await Problem.findById(problemId);
    if (!problem) {
      throw new NotFoundError('Problem');
    }

    if (!problem.visibleTestCases || problem.visibleTestCases.length === 0) {
      throw new ValidationError('No test cases available for this problem');
    }

    // Get Judge0 language ID
    const languageId = getLanguageById(language);

    // Prepare submissions for Judge0
    const submissions = problem.visibleTestCases.map((testcase) => ({
      source_code: code,
      language_id: languageId,
      stdin: testcase.input,
      expected_output: testcase.output
    }));

    logger.debug('Prepared submissions for Judge0', {
      submissionCount: submissions.length,
      languageId
    });


    // Submit to Judge0
    const submitResult = await submitBatch(submissions);
    const resultTokens = submitResult.map(result => result.token);

    logger.debug('Judge0 submission tokens received', { tokens: resultTokens });

    // Get results from Judge0
    const testResults = await submitToken(resultTokens);

    // Process results
    const testCases = testResults.map((result, index) => ({
      stdin: problem.visibleTestCases[index].input,
      expected_output: problem.visibleTestCases[index].output,
      stdout: result.stdout || '',
      stderr: result.stderr || '',
      status_id: result.status_id,
      status: getStatusMessage(result.status_id),
      execution_time: result.time,
      memory: result.memory
    }));

    const success = testResults.every(result => result.status_id === JUDGE0_STATUS.ACCEPTED);

    logger.info('Code run completed', {
      userId,
      problemId,
      success,
      passedTests: testResults.filter(r => r.status_id === JUDGE0_STATUS.ACCEPTED).length,
      totalTests: testResults.length
    });

    res.status(HTTP_STATUS.OK).json({
      success,
      testCases,
      summary: {
        passed: testResults.filter(r => r.status_id === JUDGE0_STATUS.ACCEPTED).length,
        total: testResults.length
      }
    });

  } catch (error) {
    logger.error('Error in runCode', {
      error: error.message,
      stack: error.stack,
      userId: req.result?._id,
      problemId: req.params.id
    });

    if (error instanceof ValidationError || error instanceof NotFoundError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message
      });
    }

    if (error instanceof Judge0Error) {
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Code execution service temporarily unavailable'
      });
    }

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: 'Internal server error'
    });
  }
}


module.exports = {submitCode,runCode};

