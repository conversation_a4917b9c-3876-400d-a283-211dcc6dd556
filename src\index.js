const express = require('express')
const app = express();
require('dotenv').config();
const main =  require('./config/db')
const cookieParser =  require('cookie-parser');
const authRouter = require("./routes/userAuth");
const redisClient = require('./config/redis');
const problemRouter = require("./routes/problemCreator");
const submitRouter = require("./routes/submit")
const aiRouter = require("./routes/aiChatting")
const cors = require('cors')

// console.log("Hello")

app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174'],
    credentials: true
}))

app.use(express.json());
app.use(cookieParser());

app.use('/user',authRouter);
app.use('/problem',problemRouter);
app.use('/submission',submitRouter);
app.use('/ai',aiRouter);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        redis: redisClient.isOpen ? 'connected' : 'disconnected'
    });
});


const InitalizeConnection = async ()=>{
    try{
        // Connect to MongoDB
        await main();
        console.log("✅ MongoDB Connected");

        // Try to connect to Redis, but don't fail if it doesn't work
        try {
            if (!redisClient.isOpen) {
                await redisClient.connect();
                console.log("✅ Redis Connected");
            }
        } catch (redisError) {
            console.log("⚠️ Redis connection failed, continuing without Redis:", redisError.message);
            // Continue without Redis - the app can still work
        }

        const PORT = process.env.PORT || 3000;
        app.listen(PORT, ()=>{
            console.log("🚀 Server listening at port number: " + PORT);
            console.log("🔗 API available at: http://localhost:" + PORT);
            console.log("🏥 Health check: http://localhost:" + PORT + "/health");
        })

    }
    catch(err){
        console.log("❌ Server startup error: "+err);
        process.exit(1);
    }
}


// Handle unhandled promise rejections (don't crash the server)
process.on('unhandledRejection', (reason, promise) => {
    console.error('⚠️ Unhandled Promise Rejection:', reason);
    console.error('Promise:', promise);
    // Don't exit - keep server running
});

// Handle uncaught exceptions (don't crash the server)
process.on('uncaughtException', (error) => {
    console.error('⚠️ Uncaught Exception:', error.message);
    console.error('Stack:', error.stack);
    // Don't exit - keep server running
});

InitalizeConnection();

