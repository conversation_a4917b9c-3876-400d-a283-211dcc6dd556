const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
require('dotenv').config();

const main = require('./config/db');
const redisClient = require('./config/redis');
const authRouter = require("./routes/userAuth");
const problemRouter = require("./routes/problemCreator");
const submitRouter = require("./routes/submit");
const { globalErrorHandler } = require('./utils/errorHandler');
const logger = require('./utils/logger');
const aiRouter = require('./routes/aiChatting');

const app = express();

// Middleware
app.use(logger.requestLogger);

app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174'],
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Routes
app.use('/user', authRouter);
app.use('/problem', problemRouter);
app.use('/submission', submitRouter);
app.use('/ai', aiRouter);
// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`
  });
});

// Global error handler (must be last)
app.use(globalErrorHandler);

// Database connection with retry logic
const connectDatabasesWithRetry = async (maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`🔄 Attempting MongoDB connection (attempt ${attempt}/${maxRetries})...`);

      // Try to connect to MongoDB
      await main();

      // Try to connect to Redis (with error handling)
      try {
        if (redisClient.isOpen) {
          logger.info('Redis already connected');
        } else {
          await redisClient.connect();
          logger.info('Redis connected successfully');
        }
      } catch (redisError) {
        logger.warn('Redis connection failed, continuing without Redis:', redisError.message);
        // Don't fail the entire startup for Redis issues
      }

      return; // Success, exit retry loop

    } catch (error) {
      logger.error(`Database connection attempt ${attempt} failed:`, error.message);

      if (attempt === maxRetries) {
        throw new Error(`Failed to connect to databases after ${maxRetries} attempts`);
      }

      // Wait before retrying (exponential backoff)
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      logger.info(`Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

const initializeServer = async () => {
  try {
    logger.info('Starting server initialization...');

    // Connect to databases with retry logic
    await connectDatabasesWithRetry();

    logger.success('Database connections established');

    // Start server with port conflict handling
    const PORT = process.env.PORT || 3000;

    const startServer = (port) => {
      return new Promise((resolve, reject) => {
        const server = app.listen(port, (err) => {
          if (err) {
            reject(err);
          } else {
            resolve(server);
          }
        });

        server.on('error', (err) => {
          if (err.code === 'EADDRINUSE') {
            reject(new Error(`Port ${port} is already in use`));
          } else {
            reject(err);
          }
        });
      });
    };

    let server;
    let currentPort = PORT;

    // Try to start server, if port is busy, try next port
    for (let attempts = 0; attempts < 5; attempts++) {
      try {
        server = await startServer(currentPort);
        logger.success(`Server running on port ${currentPort}`);
        logger.info(`Health check available at http://localhost:${currentPort}/health`);
        logger.info(`API base URL: http://localhost:${currentPort}`);

        if (process.env.NODE_ENV === 'development') {
          logger.info('Development mode - server will restart on file changes');
        }
        break;
      } catch (error) {
        if (error.message.includes('already in use') && attempts < 4) {
          currentPort++;
          logger.warn(`Port ${currentPort - 1} is busy, trying port ${currentPort}...`);
        } else {
          throw error;
        }
      }
    }

    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      server.close(async () => {
        logger.info('HTTP server closed');

        try {
          await redisClient.disconnect();
          logger.info('Redis connection closed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown', { error: error.message });
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to initialize server', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
};

// Handle unhandled promise rejections (don't crash the server)
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  console.error('⚠️ Unhandled Promise Rejection - Server continuing to run');
  console.error('Reason:', reason);
  // Don't exit - keep server running
});

// Handle uncaught exceptions (don't crash the server)
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  console.error('⚠️ Uncaught Exception - Server continuing to run');
  console.error('Error:', error.message);
  // Don't exit - keep server running
});

// Handle process termination gracefully
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

initializeServer();
