const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
require('dotenv').config();

const main = require('./config/db');
const redisClient = require('./config/redis');
const authRouter = require("./routes/userAuth");
const problemRouter = require("./routes/problemCreator");
const submitRouter = require("./routes/submit");
const { globalErrorHandler } = require('./utils/errorHandler');
const logger = require('./utils/logger');

const app = express();

// Middleware
app.use(logger.requestLogger);

app.use(cors({
    origin: 'http://localhost:5173',
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Routes
app.use('/user', authRouter);
app.use('/problem', problemRouter);
app.use('/submission', submitRouter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`
  });
});

// Global error handler (must be last)
app.use(globalErrorHandler);


/**
 * Initialize database connections and start server
 */
const initializeServer = async () => {
  try {
    logger.info('Starting server initialization...');

    // Connect to databases
    await Promise.all([
      main(),
      redisClient.connect()
    ]);

    logger.success('Database connections established');

    // Start server with port conflict handling
    const PORT = process.env.PORT || 3000;

    const startServer = (port) => {
      return new Promise((resolve, reject) => {
        const server = app.listen(port, (err) => {
          if (err) {
            reject(err);
          } else {
            resolve(server);
          }
        });

        server.on('error', (err) => {
          if (err.code === 'EADDRINUSE') {
            reject(new Error(`Port ${port} is already in use`));
          } else {
            reject(err);
          }
        });
      });
    };

    let server;
    let currentPort = PORT;

    // Try to start server, if port is busy, try next port
    for (let attempts = 0; attempts < 5; attempts++) {
      try {
        server = await startServer(currentPort);
        logger.success(`Server running on port ${currentPort}`);
        logger.info(`Health check available at http://localhost:${currentPort}/health`);
        logger.info(`API base URL: http://localhost:${currentPort}`);

        if (process.env.NODE_ENV === 'development') {
          logger.info('Development mode - server will restart on file changes');
        }
        break;
      } catch (error) {
        if (error.message.includes('already in use') && attempts < 4) {
          currentPort++;
          logger.warn(`Port ${currentPort - 1} is busy, trying port ${currentPort}...`);
        } else {
          throw error;
        }
      }
    }

    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      server.close(async () => {
        logger.info('HTTP server closed');

        try {
          await redisClient.disconnect();
          logger.info('Redis connection closed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown', { error: error.message });
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to initialize server', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

initializeServer();
