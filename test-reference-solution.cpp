#include <bits/stdc++.h>
using namespace std;

class Solution {
public:
    int findBound(vector<int>& nums, int target, bool isFirst) {
        int left = 0, right = nums.size() - 1, bound = -1;
        while (left <= right) {
            int mid = left + (right - left)/2;
            if (nums[mid] == target) {
                bound = mid;
                if (isFirst) right = mid - 1;
                else left = mid + 1;
            }
            else if (nums[mid] < target) left = mid + 1;
            else right = mid - 1;
        }
        return bound;
    }
    
    vector<int> searchRange(vector<int>& nums, int target) {
        return { findBound(nums, target, true),
                 findBound(nums, target, false) };
    }
};

int main() {
    int n; cin >> n;
    vector<int> nums(n);
    for (int i = 0; i < n; i++) cin >> nums[i];
    int target; cin >> target;
    
    Solution sol;
    auto ans = sol.searchRange(nums, target);
    cout << "[" << ans[0] << ", " << ans[1] << "]\n";
    return 0;
}
