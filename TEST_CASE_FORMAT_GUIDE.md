# 🔧 Test Case Format Guide - CRITICAL FOR PROBLEM CREATION

## ❌ **Why Your Problem Creation is Failing**

The backend error "Error Occured" happens because your test case format doesn't match what Judge<PERSON> expects.

### **Current Issue:**
```
❌ WRONG FORMAT (Descriptive):
input: "arr = [1, 3, 5, 7, 9]  key = 5"
output: "2"
explanation: "The element is found at index 2"
```

### **Root Cause:**
- Backend sends `testcase.input` directly to <PERSON><PERSON> as `stdin`
- Judge<PERSON> expects **actual program input**, not descriptions
- Your reference solution tries to read from stdin but gets descriptive text
- Program fails → Backend returns "Error Occured"

## ✅ **CORRECT TEST CASE FORMATS**

### **Example 1: Binary Search Problem**

**✅ CORRECT FORMAT:**
```javascript
// Visible Test Case 1:
{
  input: "5\n1 3 5 7 9\n5",           // ✅ Actual stdin input
  output: "2",                         // ✅ Expected program output
  explanation: "Array size=5, elements=[1,3,5,7,9], search for 5, found at index 2"
}

// Visible Test Case 2:
{
  input: "4\n2 4 6 8\n3",             // ✅ Actual stdin input  
  output: "-1",                        // ✅ Expected program output
  explanation: "Array size=4, elements=[2,4,6,8], search for 3, not found"
}
```

**Reference Solution (C++):**
```cpp
#include <iostream>
#include <vector>
using namespace std;

int main() {
    int n;
    cin >> n;                    // Reads "5"
    
    vector<int> arr(n);
    for(int i = 0; i < n; i++) {
        cin >> arr[i];           // Reads "1 3 5 7 9"
    }
    
    int key;
    cin >> key;                  // Reads "5"
    
    // Binary search logic
    for(int i = 0; i < n; i++) {
        if(arr[i] == key) {
            cout << i << endl;   // Outputs "2"
            return 0;
        }
    }
    
    cout << -1 << endl;          // Not found
    return 0;
}
```

### **Example 2: Two Sum Problem**

**✅ CORRECT FORMAT:**
```javascript
// Visible Test Case:
{
  input: "4\n2 7 11 15\n9",           // Array size, elements, target
  output: "0 1",                       // Indices of the two numbers
  explanation: "nums[0] + nums[1] = 2 + 7 = 9, so return indices 0 and 1"
}
```

### **Example 3: Simple Addition**

**✅ CORRECT FORMAT:**
```javascript
// Visible Test Case:
{
  input: "5 3",                        // Two numbers separated by space
  output: "8",                         // Sum
  explanation: "5 + 3 = 8"
}
```

## 🎯 **INPUT FORMAT PATTERNS**

### **Pattern 1: Array + Target**
```
Format: "n\nelement1 element2 ... elementn\ntarget"
Example: "5\n1 3 5 7 9\n5"
```

### **Pattern 2: Multiple Numbers**
```
Format: "num1 num2 num3"
Example: "10 20 30"
```

### **Pattern 3: Single Number**
```
Format: "number"
Example: "42"
```

### **Pattern 4: String Input**
```
Format: "string"
Example: "hello world"
```

## 🔧 **HOW TO FIX YOUR CURRENT PROBLEM**

### **Step 1: Update Your Test Cases**

Change from:
```javascript
❌ WRONG:
{
  input: "arr = [1, 3, 5, 7, 9]  key = 5",
  output: "2",
  explanation: "The element is found at index 2"
}
```

To:
```javascript
✅ CORRECT:
{
  input: "5\n1 3 5 7 9\n5",
  output: "2", 
  explanation: "Array size=5, elements=[1,3,5,7,9], search for 5, found at index 2"
}
```

### **Step 2: Update Your Reference Solutions**

Make sure your reference solutions read input in the same format:

**C++:**
```cpp
#include <iostream>
#include <vector>
using namespace std;

int main() {
    int n;
    cin >> n;                    // Read array size
    
    vector<int> arr(n);
    for(int i = 0; i < n; i++) {
        cin >> arr[i];           // Read array elements
    }
    
    int key;
    cin >> key;                  // Read search key
    
    // Your algorithm here
    
    return 0;
}
```

**Java:**
```java
import java.util.Scanner;

public class Solution {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        int n = sc.nextInt();           // Read array size
        int[] arr = new int[n];
        
        for(int i = 0; i < n; i++) {
            arr[i] = sc.nextInt();      // Read array elements
        }
        
        int key = sc.nextInt();         // Read search key
        
        // Your algorithm here
        
        sc.close();
    }
}
```

**JavaScript (Node.js):**
```javascript
const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

let input = [];
rl.on('line', (line) => {
    input.push(line);
});

rl.on('close', () => {
    const n = parseInt(input[0]);           // Read array size
    const arr = input[1].split(' ').map(Number);  // Read array elements
    const key = parseInt(input[2]);         // Read search key
    
    // Your algorithm here
});
```

## 🚨 **CRITICAL RULES**

1. **Input must be executable** - what the program actually reads from stdin
2. **Output must be exact** - exactly what your program prints to stdout
3. **No extra spaces or formatting** in output unless your program produces them
4. **Test your reference solution** manually with the input to verify it produces the expected output
5. **Use `\n` for newlines** in input strings

## ✅ **VERIFICATION STEPS**

Before submitting your problem:

1. **Copy your input** from the test case
2. **Run your reference solution** manually with that input
3. **Verify the output** matches exactly what you specified
4. **Check all test cases** this way

**Example verification:**
```bash
# Save input to file
echo -e "5\n1 3 5 7 9\n5" > input.txt

# Run your program
./your_program < input.txt

# Should output exactly: 2
```

## 🎉 **RESULT**

After fixing your test case format, your problem creation will work correctly and the "Error Occured" message will disappear!
