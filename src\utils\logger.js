const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const getTimestamp = () => {
  return new Date().toISOString();
};

const formatMessage = (level, message, data = null) => {
  const timestamp = getTimestamp();
  const colorMap = {
    ERROR: colors.red,
    WARN: colors.yellow,
    INFO: colors.blue,
    DEBUG: colors.cyan,
    SUCCESS: colors.green
  };

  const color = colorMap[level] || colors.white;
  const formattedMessage = `${color}[${timestamp}] ${level}:${colors.reset} ${message}`;
  
  if (data) {
    return `${formattedMessage}\n${colors.dim}${JSON.stringify(data, null, 2)}${colors.reset}`;
  }
  
  return formattedMessage;
};

const logger = {
  error: (message, data = null) => {
    console.error(formatMessage('ERROR', message, data));
  },

  warn: (message, data = null) => {
    console.warn(formatMessage('WARN', message, data));
  },

  info: (message, data = null) => {
    console.log(formatMessage('INFO', message, data));
  },

  debug: (message, data = null) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(formatMessage('DEBUG', message, data));
    }
  },

  success: (message, data = null) => {
    console.log(formatMessage('SUCCESS', message, data));
  },

  // Request logging middleware
  requestLogger: (req, res, next) => {
    const start = Date.now();
    const { method, url, ip } = req;
    
    logger.info(`${method} ${url}`, { ip, userAgent: req.get('User-Agent') });
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const { statusCode } = res;
      const level = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'info';
      
      logger[level](`${method} ${url} - ${statusCode}`, { 
        duration: `${duration}ms`,
        ip 
      });
    });
    
    next();
  }
};

module.exports = logger;
