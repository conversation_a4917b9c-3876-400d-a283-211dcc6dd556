import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import axiosClient from '../utils/axiosClient';
import { useNavigate } from 'react-router';

// Zod schema matching the backend problem schema exactly
const problemSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  difficulty: z.enum(['easy', 'medium', 'hard']),
  tags: z.enum([
    'array', 'linkedList', 'graph', 'dp', 'binarySearch', 'sorting',
    'twoPointers', 'slidingWindow', 'hashMap', 'stack', 'queue',
    'tree', 'binaryTree', 'bst', 'heap', 'greedy', 'backtracking',
    'dfs', 'bfs', 'recursion', 'math', 'string', 'matrix'
  ]),
  visibleTestCases: z.array(
    z.object({
      input: z.string().min(1, 'Input is required'),
      output: z.string().min(1, 'Output is required'),
      explanation: z.string().min(1, 'Explanation is required')
    })
  ).min(1, 'At least one visible test case required'),
  hiddenTestCases: z.array(
    z.object({
      input: z.string().min(1, 'Input is required'),
      output: z.string().min(1, 'Output is required')
    })
  ).min(1, 'At least one hidden test case required'),
  startCode: z.array(
    z.object({
      language: z.enum(['c++', 'java', 'javascript']), // Backend expects lowercase
      initialCode: z.string().min(1, 'Initial code is required')
    })
  ).length(3, 'All three languages required'),
  referenceSolution: z.array(
    z.object({
      language: z.enum(['c++', 'java', 'javascript']), // Backend expects lowercase
      completeCode: z.string().min(1, 'Complete code is required')
    })
  ).length(3, 'All three languages required')
});

function AdminPanel() {
  const navigate = useNavigate();
  const {
    register,
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(problemSchema),
    defaultValues: {
      startCode: [
        { language: 'c++', initialCode: '' },
        { language: 'java', initialCode: '' },
        { language: 'javascript', initialCode: '' }
      ],
      referenceSolution: [
        { language: 'c++', completeCode: '' },
        { language: 'java', completeCode: '' },
        { language: 'javascript', completeCode: '' }
      ]
    }
  });

  const {
    fields: visibleFields,
    append: appendVisible,
    remove: removeVisible
  } = useFieldArray({
    control,
    name: 'visibleTestCases'
  });

  const {
    fields: hiddenFields,
    append: appendHidden,
    remove: removeHidden
  } = useFieldArray({
    control,
    name: 'hiddenTestCases'
  });

  const onSubmit = async (data) => {
    try {
      console.log('📤 Submitting problem data:', data);

      // Validate that all required fields are present
      if (!data.title || !data.description || !data.difficulty || !data.tags) {
        throw new Error('Missing required fields: title, description, difficulty, or tags');
      }

      if (!data.visibleTestCases || data.visibleTestCases.length === 0) {
        throw new Error('At least one visible test case is required');
      }

      if (!data.hiddenTestCases || data.hiddenTestCases.length === 0) {
        throw new Error('At least one hidden test case is required');
      }

      if (!data.startCode || data.startCode.length !== 3) {
        throw new Error('Start code for all three languages (c++, java, javascript) is required');
      }

      if (!data.referenceSolution || data.referenceSolution.length !== 3) {
        throw new Error('Reference solution for all three languages (c++, java, javascript) is required');
      }

      // Validate that all start codes have content
      for (const startCode of data.startCode) {
        if (!startCode.initialCode || startCode.initialCode.trim() === '') {
          throw new Error(`Initial code for ${startCode.language} is required`);
        }
      }

      // Validate that all reference solutions have content
      for (const refSol of data.referenceSolution) {
        if (!refSol.completeCode || refSol.completeCode.trim() === '') {
          throw new Error(`Reference solution for ${refSol.language} is required`);
        }
      }

      console.log('✅ All validations passed, submitting to backend...');
      console.log('📤 Sending data:', JSON.stringify(data, null, 2));

      const response = await axiosClient.post('/problem/create', data);
      console.log('✅ Problem created successfully:', response.data);

      alert('Problem created successfully!');
      navigate('/admin');
    } catch (error) {
      console.error('❌ Error creating problem:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);

      let errorMessage = 'Unknown error occurred';

      if (error.message && !error.response) {
        // Client-side validation error
        errorMessage = error.message;
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login as admin.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access denied. Admin privileges required.';
      } else if (error.response?.status === 400) {
        const backendError = error.response?.data || 'Bad request. Please check your input.';
        if (backendError === 'Error Occured') {
          errorMessage = 'Reference solution validation failed. Please check:\n' +
                        '1. Test case input format (use actual stdin input, not descriptions)\n' +
                        '2. Reference solutions produce correct output for test cases\n' +
                        '3. Input format: "5\\n1 3 5 7 9\\n5" not "arr = [1,3,5,7,9] key = 5"\n' +
                        'See TEST_CASE_FORMAT_GUIDE.md for details.';
        } else {
          errorMessage = backendError;
        }
      } else if (error.response?.data) {
        errorMessage = typeof error.response.data === 'string'
          ? error.response.data
          : error.response.data.message || error.response.data.error || 'Server error';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`Error creating problem: ${errorMessage}`);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Create New Problem</h1>

      {/* Instructions */}
      <div className="alert alert-info mb-6">
        <div>
          <h3 className="font-bold">📝 Important Instructions:</h3>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li><strong>Test Case Input:</strong> Use actual line breaks (press Enter), not \n characters</li>
            <li><strong>Input Format:</strong> First line = array size, second line = array elements, third line = target</li>
            <li><strong>Reference Solutions:</strong> Must include main() function to read input and produce output</li>
            <li><strong>Output:</strong> Should be exactly what your program prints (no extra text)</li>
          </ul>
        </div>
      </div>
      
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-6"
        onKeyDown={(e) => {
          // Prevent form submission on Enter key except for submit button
          if (e.key === 'Enter' && e.target.type !== 'submit' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
          }
        }}
      >
        {/* Basic Information */}
        <div className="card bg-base-100 shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
          <div className="space-y-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Title</span>
              </label>
              <input
                {...register('title')}
                className={`input input-bordered ${errors.title && 'input-error'}`}
              />
              {errors.title && (
                <span className="text-error">{errors.title.message}</span>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Description</span>
              </label>
              <textarea
                {...register('description')}
                className={`textarea textarea-bordered h-32 ${errors.description && 'textarea-error'}`}
              />
              {errors.description && (
                <span className="text-error">{errors.description.message}</span>
              )}
            </div>

            <div className="flex gap-4">
              <div className="form-control w-1/2">
                <label className="label">
                  <span className="label-text">Difficulty</span>
                </label>
                <select
                  {...register('difficulty')}
                  className={`select select-bordered ${errors.difficulty && 'select-error'}`}
                >
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
              </div>

              <div className="form-control w-1/2">
                <label className="label">
                  <span className="label-text">Problem Category/Tag</span>
                </label>
                <select
                  {...register('tags')}
                  className={`select select-bordered ${errors.tags && 'select-error'}`}
                >
                  <option value="">Select a category...</option>
                  <optgroup label="Data Structures">
                    <option value="array">Array</option>
                    <option value="linkedList">Linked List</option>
                    <option value="stack">Stack</option>
                    <option value="queue">Queue</option>
                    <option value="hashMap">Hash Map</option>
                    <option value="tree">Tree</option>
                    <option value="binaryTree">Binary Tree</option>
                    <option value="bst">Binary Search Tree</option>
                    <option value="heap">Heap</option>
                    <option value="matrix">Matrix</option>
                  </optgroup>
                  <optgroup label="Algorithms">
                    <option value="binarySearch">Binary Search</option>
                    <option value="sorting">Sorting</option>
                    <option value="twoPointers">Two Pointers</option>
                    <option value="slidingWindow">Sliding Window</option>
                    <option value="dfs">Depth First Search (DFS)</option>
                    <option value="bfs">Breadth First Search (BFS)</option>
                    <option value="backtracking">Backtracking</option>
                    <option value="greedy">Greedy</option>
                    <option value="dp">Dynamic Programming</option>
                    <option value="recursion">Recursion</option>
                  </optgroup>
                  <optgroup label="Problem Types">
                    <option value="graph">Graph</option>
                    <option value="string">String</option>
                    <option value="math">Math</option>
                  </optgroup>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Test Cases */}
        <div className="card bg-base-100 shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Cases</h2>
          
          {/* Visible Test Cases */}
          <div className="space-y-4 mb-6">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Visible Test Cases</h3>
              <button
                type="button"
                onClick={() => appendVisible({ input: '', output: '', explanation: '' })}
                className="btn btn-sm btn-primary"
              >
                Add Visible Case
              </button>
            </div>
            
            {visibleFields.map((field, index) => (
              <div key={field.id} className="border p-4 rounded-lg space-y-2">
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => removeVisible(index)}
                    className="btn btn-xs btn-error"
                  >
                    Remove
                  </button>
                </div>
                
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Input (use actual line breaks, not \n)</span>
                  </label>
                  <textarea
                    {...register(`visibleTestCases.${index}.input`)}
                    placeholder="Example:&#10;5&#10;1 3 5 7 9&#10;5"
                    className="textarea textarea-bordered w-full h-24 font-mono"
                    onKeyDown={(e) => {
                      // Allow Enter key in textarea
                      e.stopPropagation();
                    }}
                  />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Expected Output</span>
                  </label>
                  <input
                    {...register(`visibleTestCases.${index}.output`)}
                    placeholder="2"
                    className="input input-bordered w-full font-mono"
                  />
                </div>
                
                <textarea
                  {...register(`visibleTestCases.${index}.explanation`)}
                  placeholder="Explanation"
                  className="textarea textarea-bordered w-full"
                />
              </div>
            ))}
          </div>

          {/* Hidden Test Cases */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Hidden Test Cases</h3>
              <button
                type="button"
                onClick={() => appendHidden({ input: '', output: '' })}
                className="btn btn-sm btn-primary"
              >
                Add Hidden Case
              </button>
            </div>
            
            {hiddenFields.map((field, index) => (
              <div key={field.id} className="border p-4 rounded-lg space-y-2">
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => removeHidden(index)}
                    className="btn btn-xs btn-error"
                  >
                    Remove
                  </button>
                </div>
                
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Input (use actual line breaks, not \n)</span>
                  </label>
                  <textarea
                    {...register(`hiddenTestCases.${index}.input`)}
                    placeholder="Example:&#10;3&#10;10 20 30&#10;20"
                    className="textarea textarea-bordered w-full h-24 font-mono"
                    onKeyDown={(e) => {
                      // Allow Enter key in textarea
                      e.stopPropagation();
                    }}
                  />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Expected Output</span>
                  </label>
                  <input
                    {...register(`hiddenTestCases.${index}.output`)}
                    placeholder="1"
                    className="input input-bordered w-full font-mono"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Code Templates */}
        <div className="card bg-base-100 shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Code Templates</h2>
          
          <div className="space-y-6">
            {[0, 1, 2].map((index) => (
              <div key={index} className="space-y-2">
                <h3 className="font-medium">
                  {index === 0 ? 'C++' : index === 1 ? 'Java' : 'JavaScript'}
                </h3>
                
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Initial Code</span>
                  </label>
                  <pre className="bg-base-300 p-4 rounded-lg">
                    <textarea
                      {...register(`startCode.${index}.initialCode`)}
                      className="w-full bg-transparent font-mono"
                      rows={6}
                    />
                  </pre>
                </div>
                
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Reference Solution</span>
                  </label>
                  <pre className="bg-base-300 p-4 rounded-lg">
                    <textarea
                      {...register(`referenceSolution.${index}.completeCode`)}
                      className="w-full bg-transparent font-mono"
                      rows={6}
                    />
                  </pre>
                </div>
              </div>
            ))}
          </div>
        </div>

        <button type="submit" className="btn btn-primary w-full">
          Create Problem
        </button>
      </form>
    </div>
  );
}

export default AdminPanel;