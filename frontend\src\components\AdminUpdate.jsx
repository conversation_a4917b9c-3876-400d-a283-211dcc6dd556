import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import axiosClient from '../utils/axiosClient';
import { useNavigate } from 'react-router';
import { Search, Edit, Save, X } from 'lucide-react';

// Zod schema for problem updates (all fields optional) - matching backend exactly
const updateProblemSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  tags: z.enum(['array', 'linkedList', 'graph', 'dp']).optional(), // Match backend enum exactly
  visibleTestCases: z.array(
    z.object({
      input: z.string().min(1, 'Input is required'),
      output: z.string().min(1, 'Output is required'),
      explanation: z.string().min(1, 'Explanation is required')
    })
  ).optional(),
  hiddenTestCases: z.array(
    z.object({
      input: z.string().min(1, 'Input is required'),
      output: z.string().min(1, 'Output is required'),
      explanation: z.string().min(1, 'Explanation is required')
    })
  ).optional(),
  startCode: z.array(
    z.object({
      language: z.enum(['c++', 'java', 'javascript']),
      initialCode: z.string().min(1, 'Initial code is required')
    })
  ).optional(),
  referenceSolution: z.array(
    z.object({
      language: z.enum(['c++', 'java', 'javascript']),
      completeCode: z.string().min(1, 'Complete code is required')
    })
  ).optional()
});

function AdminUpdate() {
  const navigate = useNavigate();
  const [problems, setProblems] = useState([]);
  const [selectedProblem, setSelectedProblem] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(updateProblemSchema)
  });

  const {
    fields: visibleFields,
    append: appendVisible,
    remove: removeVisible,
    replace: replaceVisible
  } = useFieldArray({
    control,
    name: 'visibleTestCases'
  });

  const {
    fields: hiddenFields,
    append: appendHidden,
    remove: removeHidden,
    replace: replaceHidden
  } = useFieldArray({
    control,
    name: 'hiddenTestCases'
  });

  // Fetch all problems on component mount
  useEffect(() => {
    fetchProblems();
  }, []);

  const fetchProblems = async () => {
    try {
      setLoading(true);
      const response = await axiosClient.get('/problem/getAllProblem');
      setProblems(response.data || []);
    } catch (error) {
      console.error('Error fetching problems:', error);
      alert('Failed to fetch problems');
    } finally {
      setLoading(false);
    }
  };

  const fetchProblemDetails = async (problemId) => {
    try {
      setLoading(true);
      const response = await axiosClient.get(`/problem/problemById/${problemId}`);
      const problem = response.data;
      
      setSelectedProblem(problem);
      
      // Reset form with problem data
      reset({
        title: problem.title,
        description: problem.description,
        difficulty: problem.difficulty,
        tags: problem.tags,
        startCode: problem.startCode || [
          { language: 'c++', initialCode: '' },
          { language: 'java', initialCode: '' },
          { language: 'javascript', initialCode: '' }
        ],
        referenceSolution: problem.referenceSolution || [
          { language: 'c++', completeCode: '' },
          { language: 'java', completeCode: '' },
          { language: 'javascript', completeCode: '' }
        ]
      });

      // Set test cases
      if (problem.visibleTestCases) {
        replaceVisible(problem.visibleTestCases);
      }
      if (problem.hiddenTestCases) {
        replaceHidden(problem.hiddenTestCases);
      }

      setIsEditing(true);
    } catch (error) {
      console.error('Error fetching problem details:', error);
      alert('Failed to fetch problem details');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    if (!selectedProblem) return;

    try {
      setLoading(true);
      console.log('Updating problem:', data);
      
      const response = await axiosClient.put(`/problem/update/${selectedProblem._id}`, data);
      
      if (response.data.success) {
        alert('Problem updated successfully!');
        setIsEditing(false);
        setSelectedProblem(null);
        reset();
        fetchProblems(); // Refresh the list
      }
    } catch (error) {
      console.error('Error updating problem:', error);
      
      let errorMessage = 'Unknown error occurred';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data) {
        errorMessage = typeof error.response.data === 'string' 
          ? error.response.data 
          : error.response.data.message || 'Update failed';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const filteredProblems = problems.filter(problem =>
    problem.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    problem.difficulty?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    problem.tags?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const cancelEdit = () => {
    setIsEditing(false);
    setSelectedProblem(null);
    reset();
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Update Problem</h1>
        {isEditing && (
          <button
            onClick={cancelEdit}
            className="btn btn-ghost"
          >
            <X size={20} />
            Cancel
          </button>
        )}
      </div>

      {!isEditing ? (
        // Problem Selection View
        <div className="space-y-6">
          {/* Search Bar */}
          <div className="form-control">
            <div className="input-group">
              <span>
                <Search size={20} />
              </span>
              <input
                type="text"
                placeholder="Search problems by title, difficulty, or tag..."
                className="input input-bordered w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Problems List */}
          <div className="card bg-base-100 shadow-lg">
            <div className="card-body">
              <h2 className="card-title mb-4">Select a Problem to Update</h2>
              
              {loading ? (
                <div className="flex justify-center py-8">
                  <span className="loading loading-spinner loading-lg"></span>
                </div>
              ) : filteredProblems.length === 0 ? (
                <div className="text-center py-8 text-base-content/60">
                  {searchTerm ? 'No problems found matching your search.' : 'No problems available.'}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="table table-zebra">
                    <thead>
                      <tr>
                        <th>Title</th>
                        <th>Difficulty</th>
                        <th>Tags</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredProblems.map((problem) => (
                        <tr key={problem._id}>
                          <td className="font-medium">{problem.title}</td>
                          <td>
                            <span className={`badge ${
                              problem.difficulty === 'easy' ? 'badge-success' :
                              problem.difficulty === 'medium' ? 'badge-warning' :
                              'badge-error'
                            }`}>
                              {problem.difficulty}
                            </span>
                          </td>
                          <td>{problem.tags}</td>
                          <td>
                            <button
                              onClick={() => fetchProblemDetails(problem._id)}
                              className="btn btn-sm btn-primary"
                              disabled={loading}
                            >
                              <Edit size={16} />
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        // Problem Edit Form
        <div className="space-y-6">
          <div className="alert alert-info">
            <div>
              <h3 className="font-bold">Editing: {selectedProblem?.title}</h3>
              <div className="text-xs">Problem ID: {selectedProblem?._id}</div>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="card bg-base-100 shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Title</span>
                  </label>
                  <input
                    {...register('title')}
                    className={`input input-bordered ${errors.title && 'input-error'}`}
                  />
                  {errors.title && (
                    <span className="text-error">{errors.title.message}</span>
                  )}
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Description</span>
                  </label>
                  <textarea
                    {...register('description')}
                    className={`textarea textarea-bordered h-32 ${errors.description && 'textarea-error'}`}
                  />
                  {errors.description && (
                    <span className="text-error">{errors.description.message}</span>
                  )}
                </div>

                <div className="flex gap-4">
                  <div className="form-control w-1/2">
                    <label className="label">
                      <span className="label-text">Difficulty</span>
                    </label>
                    <select
                      {...register('difficulty')}
                      className={`select select-bordered ${errors.difficulty && 'select-error'}`}
                    >
                      <option value="easy">Easy</option>
                      <option value="medium">Medium</option>
                      <option value="hard">Hard</option>
                    </select>
                  </div>

                  <div className="form-control w-1/2">
                    <label className="label">
                      <span className="label-text">Tag</span>
                    </label>
                    <select
                      {...register('tags')}
                      className={`select select-bordered ${errors.tags && 'select-error'}`}
                    >
                      <option value="array">Array</option>
                      <option value="linked list">Linked List</option>
                      <option value="graph">Graph</option>
                      <option value="dp">DP</option>
                      <option value="binary-search">Binary Search</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Test Cases */}
            <div className="card bg-base-100 shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Test Cases</h2>

              {/* Visible Test Cases */}
              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Visible Test Cases</h3>
                  <button
                    type="button"
                    onClick={() => appendVisible({ input: '', output: '', explanation: '' })}
                    className="btn btn-sm btn-primary"
                  >
                    Add Visible Case
                  </button>
                </div>

                {visibleFields.map((field, index) => (
                  <div key={field.id} className="border p-4 rounded-lg space-y-2">
                    <div className="flex justify-end">
                      <button
                        type="button"
                        onClick={() => removeVisible(index)}
                        className="btn btn-xs btn-error"
                      >
                        Remove
                      </button>
                    </div>

                    <input
                      {...register(`visibleTestCases.${index}.input`)}
                      placeholder="Input"
                      className="input input-bordered w-full"
                    />

                    <input
                      {...register(`visibleTestCases.${index}.output`)}
                      placeholder="Output"
                      className="input input-bordered w-full"
                    />

                    <textarea
                      {...register(`visibleTestCases.${index}.explanation`)}
                      placeholder="Explanation"
                      className="textarea textarea-bordered w-full"
                    />
                  </div>
                ))}
              </div>

              {/* Hidden Test Cases */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Hidden Test Cases</h3>
                  <button
                    type="button"
                    onClick={() => appendHidden({ input: '', output: '', explanation: '' })}
                    className="btn btn-sm btn-primary"
                  >
                    Add Hidden Case
                  </button>
                </div>

                {hiddenFields.map((field, index) => (
                  <div key={field.id} className="border p-4 rounded-lg space-y-2">
                    <div className="flex justify-end">
                      <button
                        type="button"
                        onClick={() => removeHidden(index)}
                        className="btn btn-xs btn-error"
                      >
                        Remove
                      </button>
                    </div>

                    <input
                      {...register(`hiddenTestCases.${index}.input`)}
                      placeholder="Input"
                      className="input input-bordered w-full"
                    />

                    <input
                      {...register(`hiddenTestCases.${index}.output`)}
                      placeholder="Output"
                      className="input input-bordered w-full"
                    />

                    <textarea
                      {...register(`hiddenTestCases.${index}.explanation`)}
                      placeholder="Explanation"
                      className="textarea textarea-bordered w-full"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Code Templates */}
            <div className="card bg-base-100 shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Code Templates</h2>

              <div className="space-y-6">
                {[0, 1, 2].map((index) => (
                  <div key={index} className="space-y-2">
                    <h3 className="font-medium">
                      {index === 0 ? 'C++' : index === 1 ? 'Java' : 'JavaScript'}
                    </h3>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">Initial Code</span>
                      </label>
                      <pre className="bg-base-300 p-4 rounded-lg">
                        <textarea
                          {...register(`startCode.${index}.initialCode`)}
                          className="w-full bg-transparent font-mono"
                          rows={6}
                        />
                      </pre>
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">Reference Solution</span>
                      </label>
                      <pre className="bg-base-300 p-4 rounded-lg">
                        <textarea
                          {...register(`referenceSolution.${index}.completeCode`)}
                          className="w-full bg-transparent font-mono"
                          rows={6}
                        />
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 justify-end">
              <button
                type="button"
                onClick={cancelEdit}
                className="btn btn-ghost"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <span className="loading loading-spinner loading-sm"></span>
                ) : (
                  <>
                    <Save size={20} />
                    Update Problem
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}

export default AdminUpdate;
