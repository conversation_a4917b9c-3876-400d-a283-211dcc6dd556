#include <bits/stdc++.h>
using namespace std;

class Solution {
public:
    int searchElement(vector<int>& arr, int target) {
        for (int i = 0; i < (int)arr.size(); ++i) {
            if (arr[i] == target) return i;
        }
        return -1;
    }
};

int main() {
    int n;
    cin >> n;
    
    vector<int> arr(n);
    for (int i = 0; i < n; i++) {
        cin >> arr[i];
    }
    
    int target;
    cin >> target;
    
    Solution sol;
    int result = sol.searchElement(arr, target);
    cout << result << endl;
    
    return 0;
}
