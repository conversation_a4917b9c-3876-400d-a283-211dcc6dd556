// Application constants
const LANGUAGES = {
  CPP: 'c++',
  JAVA: 'java',
  JAVASCRIPT: 'javascript'
};

const JUDGE0_LANGUAGE_IDS = {
  [LANGUAGES.CPP]: 54,
  [LANGUAGES.JAVA]: 62,
  [LANGUAGES.JAVASCRIPT]: 63
};

const SUBMISSION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  WRONG: 'wrong',
  ERROR: 'error'
};

const JUDGE0_STATUS = {
  IN_QUEUE: 1,
  PROCESSING: 2,
  ACCEPTED: 3,
  WRONG_ANSWER: 4,
  TIME_LIMIT_EXCEEDED: 5,
  COMPILATION_ERROR: 6,
  RUNTIME_ERROR_SIGSEGV: 7,
  RUNTIME_ERROR_SIGXFSZ: 8,
  RUNTIME_ERROR_SIGFPE: 9,
  RUNTIME_ERROR_SIGABRT: 10,
  RUNTIME_ERROR_NZEC: 11,
  RUNTIME_ERROR_OTHER: 12,
  INTERNAL_ERROR: 13,
  EXEC_FORMAT_ERROR: 14
};

const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
};

const PROBLEM_TAGS = {
  ARRAY: 'array',
  LINKED_LIST: 'linked list',
  GRAPH: 'graph',
  DYNAMIC_PROGRAMMING: 'dp',
  BINARY_SEARCH: 'binary-search'
};

const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

const API_TIMEOUTS = {
  JUDGE0_SUBMISSION: 30000, // 30 seconds
  JUDGE0_RESULT_POLLING: 1000, // 1 second
  MAX_POLLING_ATTEMPTS: 30
};

module.exports = {
  LANGUAGES,
  JUDGE0_LANGUAGE_IDS,
  SUBMISSION_STATUS,
  JUDGE0_STATUS,
  DIFFICULTY_LEVELS,
  PROBLEM_TAGS,
  HTTP_STATUS,
  API_TIMEOUTS
};
