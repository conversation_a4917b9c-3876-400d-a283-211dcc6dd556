#!/usr/bin/env node

const { exec } = require('child_process');
const os = require('os');

/**
 * Development setup script to handle common development tasks
 */

const isWindows = os.platform() === 'win32';

const killPort = (port) => {
  return new Promise((resolve, reject) => {
    const command = isWindows 
      ? `netstat -ano | findstr :${port}`
      : `lsof -ti:${port}`;
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.log(`No process found on port ${port}`);
        resolve();
        return;
      }

      if (isWindows) {
        // Parse Windows netstat output
        const lines = stdout.split('\n').filter(line => line.includes('LISTENING'));
        if (lines.length > 0) {
          const pid = lines[0].trim().split(/\s+/).pop();
          exec(`taskkill /PID ${pid} /F`, (killError) => {
            if (killError) {
              console.error(`Failed to kill process ${pid}:`, killError.message);
              reject(killError);
            } else {
              console.log(`✅ Killed process ${pid} on port ${port}`);
              resolve();
            }
          });
        } else {
          resolve();
        }
      } else {
        // Unix/Linux/Mac
        const pids = stdout.trim().split('\n').filter(pid => pid);
        if (pids.length > 0) {
          exec(`kill -9 ${pids.join(' ')}`, (killError) => {
            if (killError) {
              console.error(`Failed to kill processes:`, killError.message);
              reject(killError);
            } else {
              console.log(`✅ Killed processes ${pids.join(', ')} on port ${port}`);
              resolve();
            }
          });
        } else {
          resolve();
        }
      }
    });
  });
};

const checkDependencies = () => {
  return new Promise((resolve, reject) => {
    exec('npm list --depth=0', (error, stdout, stderr) => {
      if (error) {
        console.log('⚠️  Some dependencies might be missing. Run: npm install');
      } else {
        console.log('✅ Dependencies check passed');
      }
      resolve();
    });
  });
};

const checkEnvironment = () => {
  const requiredEnvVars = ['DB_CONNECT_STRING', 'JWT_KEY', 'JUDGE0_KEY'];
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.log('⚠️  Missing environment variables:', missing.join(', '));
    console.log('   Make sure your .env file is properly configured');
  } else {
    console.log('✅ Environment variables check passed');
  }
};

const main = async () => {
  console.log('🚀 Setting up development environment...\n');
  
  try {
    // Load environment variables
    require('dotenv').config();
    
    // Check environment
    checkEnvironment();
    
    // Check dependencies
    await checkDependencies();
    
    // Kill any processes on port 3000
    console.log('🔍 Checking for processes on port 3000...');
    await killPort(3000);
    
    console.log('\n✅ Development environment ready!');
    console.log('\nAvailable commands:');
    console.log('  npm run dev        - Start development server with nodemon');
    console.log('  npm run start      - Start production server');
    console.log('  npm run kill-port  - Kill processes on port 3000');
    console.log('  npm run clean-start - Kill port and start dev server');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { killPort, checkDependencies, checkEnvironment };
