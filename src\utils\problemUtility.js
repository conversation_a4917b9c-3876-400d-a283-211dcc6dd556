const axios = require('axios');
const { JUDGE0_LANGUAGE_IDS, JUDGE0_STATUS, API_TIMEOUTS } = require('./constants');
const { Judge0Error, ValidationError } = require('./errorHandler');
const logger = require('./logger');

/**
 * Get Judge0 language ID for a given language
 * @param {string} language - The programming language
 * @returns {number} Judge0 language ID
 * @throws {ValidationError} If language is not supported
 */
const getLanguageById = (language) => {
  if (!language || typeof language !== 'string') {
    throw new ValidationError('Language must be a non-empty string');
  }

  const normalizedLang = language.toLowerCase().trim();
  const languageId = JUDGE0_LANGUAGE_IDS[normalizedLang];

  if (!languageId) {
    const supportedLanguages = Object.keys(JUDGE0_LANGUAGE_IDS).join(', ');
    throw new ValidationError(
      `Unsupported language: ${language}. Supported languages: ${supportedLanguages}`
    );
  }

  return languageId;
};


/**
 * Submit a batch of code submissions to Judge0
 * @param {Array} submissions - Array of submission objects
 * @returns {Promise<Array>} Array of submission results with tokens
 * @throws {Judge0Error} If submission fails
 */
const submitBatch = async (submissions) => {
  if (!process.env.JUDGE0_KEY) {
    throw new Judge0Error('JUDGE0_KEY environment variable is not set');
  }

  if (!Array.isArray(submissions) || submissions.length === 0) {
    throw new ValidationError('Submissions must be a non-empty array');
  }

  // Validate each submission
  submissions.forEach((submission, index) => {
    if (!submission.source_code || typeof submission.source_code !== 'string') {
      throw new ValidationError(`Submission ${index}: source_code is required`);
    }
    if (!submission.language_id || typeof submission.language_id !== 'number') {
      throw new ValidationError(`Submission ${index}: language_id is required`);
    }
    if (!submission.stdin || typeof submission.stdin !== 'string') {
      throw new ValidationError(`Submission ${index}: stdin is required`);
    }
  });

  const options = {
    method: 'POST',
    url: 'https://judge0-ce.p.rapidapi.com/submissions/batch',
    params: {
      base64_encoded: 'false'
    },
    headers: {
      'x-rapidapi-key': process.env.JUDGE0_KEY,
      'x-rapidapi-host': 'judge0-ce.p.rapidapi.com',
      'Content-Type': 'application/json'
    },
    data: {
      submissions
    },
    timeout: API_TIMEOUTS.JUDGE0_SUBMISSION
  };

  try {
    logger.debug('Submitting batch to Judge0', { submissionCount: submissions.length });
    const response = await axios.request(options);

    if (!response.data || !Array.isArray(response.data)) {
      throw new Judge0Error('Invalid response format from Judge0 batch submission');
    }

    logger.debug('Judge0 batch submission successful', {
      tokens: response.data.map(r => r.token)
    });

    return response.data;
  } catch (error) {
    logger.error('Error in submitBatch', {
      error: error.message,
      submissionCount: submissions.length
    });

    if (error.response) {
      throw new Judge0Error(
        `HTTP ${error.response.status}: ${error.response.data?.message || error.message}`,
        error
      );
    }

    throw new Judge0Error(error.message, error);
  }
};


/**
 * Wait for a specified amount of time
 * @param {number} milliseconds - Time to wait in milliseconds
 * @returns {Promise<void>}
 */
const wait = async (milliseconds) => {
  return new Promise(resolve => {
    setTimeout(resolve, milliseconds);
  });
};

/**
 * Retrieve results for submitted tokens from Judge0
 * @param {Array<string>} resultTokens - Array of submission tokens
 * @returns {Promise<Array>} Array of submission results
 * @throws {Judge0Error} If retrieval fails or times out
 */
const submitToken = async (resultTokens) => {
  if (!process.env.JUDGE0_KEY) {
    throw new Judge0Error('JUDGE0_KEY environment variable is not set');
  }

  if (!Array.isArray(resultTokens) || resultTokens.length === 0) {
    throw new ValidationError('Result tokens must be a non-empty array');
  }

  // Validate tokens
  resultTokens.forEach((token, index) => {
    if (!token || typeof token !== 'string') {
      throw new ValidationError(`Token ${index} must be a non-empty string`);
    }
  });

  const options = {
    method: 'GET',
    url: 'https://judge0-ce.p.rapidapi.com/submissions/batch',
    params: {
      tokens: resultTokens.join(','),
      base64_encoded: 'false',
      fields: '*'
    },
    headers: {
      'x-rapidapi-key': process.env.JUDGE0_KEY,
      'x-rapidapi-host': 'judge0-ce.p.rapidapi.com'
    },
    timeout: API_TIMEOUTS.JUDGE0_SUBMISSION
  };

  const fetchResults = async () => {
    try {
      const response = await axios.request(options);

      if (!response.data || !response.data.submissions) {
        throw new Judge0Error('Invalid response format from Judge0 token retrieval');
      }

      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Judge0Error(
          `HTTP ${error.response.status}: ${error.response.data?.message || error.message}`,
          error
        );
      }
      throw new Judge0Error(error.message, error);
    }
  };

  let attempts = 0;
  const maxAttempts = API_TIMEOUTS.MAX_POLLING_ATTEMPTS;

  logger.debug('Starting to poll Judge0 results', {
    tokens: resultTokens,
    maxAttempts
  });

  while (attempts < maxAttempts) {
    try {
      const result = await fetchResults();

      // Check if all submissions are completed (status_id > 2)
      const allCompleted = result.submissions.every(submission =>
        submission.status_id > JUDGE0_STATUS.PROCESSING
      );

      if (allCompleted) {
        logger.debug('All Judge0 submissions completed', {
          attempts: attempts + 1,
          results: result.submissions.map(s => ({
            token: s.token,
            status_id: s.status_id
          }))
        });
        return result.submissions;
      }

      attempts++;
      logger.debug(`Judge0 polling attempt ${attempts}/${maxAttempts}`, {
        completedCount: result.submissions.filter(s => s.status_id > JUDGE0_STATUS.PROCESSING).length,
        totalCount: result.submissions.length
      });

      await wait(API_TIMEOUTS.JUDGE0_RESULT_POLLING);
    } catch (error) {
      logger.error(`Judge0 polling attempt ${attempts + 1} failed`, { error: error.message });
      attempts++;

      if (attempts >= maxAttempts) {
        throw error;
      }

      await wait(API_TIMEOUTS.JUDGE0_RESULT_POLLING);
    }
  }

  throw new Judge0Error(
    `Timeout waiting for Judge0 results after ${maxAttempts} attempts (${maxAttempts * API_TIMEOUTS.JUDGE0_RESULT_POLLING / 1000}s)`
  );
};


module.exports = {
  getLanguageById,
  submitBatch,
  submitToken,
  wait
};







