const fs = require('fs');
const path = require('path');

console.log('🔍 Validating CodeEditor Environment...\n');

// Check required files
const requiredFiles = [
    'src/index.js',
    'src/config/db.js',
    'src/config/redis.js',
    'frontend/.env',
    '.env'
];

let allValid = true;

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`);
    } else {
        console.log(`   ❌ ${file} - MISSING`);
        allValid = false;
    }
});

// Check frontend environment
console.log('\n🔧 Checking frontend environment:');
try {
    const frontendEnv = fs.readFileSync('frontend/.env', 'utf8');
    if (frontendEnv.includes('VITE_API_BASE_URL=http://localhost:3003')) {
        console.log('   ✅ Frontend configured for port 3003');
    } else if (frontendEnv.includes('VITE_API_BASE_URL')) {
        console.log('   ⚠️ Frontend configured for different port');
        console.log('   📝 Current config:', frontendEnv.match(/VITE_API_BASE_URL=.*/)?.[0]);
    } else {
        console.log('   ❌ VITE_API_BASE_URL not found');
        allValid = false;
    }
} catch (error) {
    console.log('   ❌ Cannot read frontend/.env');
    allValid = false;
}

// Check backend environment
console.log('\n🔧 Checking backend environment:');
try {
    const backendEnv = fs.readFileSync('.env', 'utf8');
    const requiredVars = [
        { name: 'DB_CONNECT_STRING', alt: 'MONGO_URI' },
        { name: 'JWT_KEY', alt: 'JWT_SECRET' },
        { name: 'GEMINI_KEY', alt: 'GEMINI_API_KEY' }
    ];

    requiredVars.forEach(varConfig => {
        const varName = varConfig.name;
        const altName = varConfig.alt;

        if (backendEnv.includes(varName) || backendEnv.includes(altName)) {
            const foundName = backendEnv.includes(varName) ? varName : altName;
            console.log(`   ✅ ${foundName} configured`);
        } else {
            console.log(`   ❌ ${varName} (or ${altName}) missing`);
            allValid = false;
        }
    });
} catch (error) {
    console.log('   ❌ Cannot read .env file');
    allValid = false;
}

// Check package.json scripts
console.log('\n📦 Checking package.json scripts:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};
    
    if (scripts.start) {
        console.log('   ✅ npm start script available');
    } else {
        console.log('   ❌ npm start script missing');
        allValid = false;
    }
    
    if (scripts['start-simple']) {
        console.log('   ✅ npm run start-simple script available');
    } else {
        console.log('   ⚠️ npm run start-simple script missing (optional)');
    }
} catch (error) {
    console.log('   ❌ Cannot read package.json');
    allValid = false;
}

console.log('\n' + '='.repeat(50));

if (allValid) {
    console.log('🎉 ENVIRONMENT VALIDATION PASSED!');
    console.log('\n✅ Your setup is ready for restart-proof operation');
    console.log('✅ Login will work consistently after restarts');
    console.log('✅ All required files and configurations are present');
    console.log('\n🚀 To start the application:');
    console.log('   • Double-click: start-both.bat');
    console.log('   • Or run: npm start');
} else {
    console.log('❌ ENVIRONMENT VALIDATION FAILED!');
    console.log('\n🔧 Please fix the issues above before starting the application');
    console.log('📖 Refer to MANUAL_FIX_CONNECTION_ERRORS.md for detailed instructions');
}

console.log('\n' + '='.repeat(50));
