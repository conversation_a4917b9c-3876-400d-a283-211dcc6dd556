const axios = require('axios');

async function testAI() {
    try {
        // First register a test user
        console.log('📝 Registering test user...');
        try {
            await axios.post('http://localhost:3003/user/register', {
                firstName: 'Test',
                lastName: 'User',
                emailId: '<EMAIL>',
                password: 'password123'
            });
            console.log('✅ User registered');
        } catch (regError) {
            console.log('ℹ️ User might already exist, proceeding to login...');
        }

        // Then login to get token
        console.log('🔐 Logging in...');
        const loginResponse = await axios.post('http://localhost:3003/user/login', {
            emailId: '<EMAIL>',
            password: 'password123'
        });
        
        const token = loginResponse.data.token;
        const cookies = loginResponse.headers['set-cookie'];
        console.log('✅ Login successful');

        // Test AI chat
        console.log('🤖 Testing AI chat...');
        const aiResponse = await axios.post('http://localhost:3003/ai/chat', {
            messages: [
                { role: 'user', content: 'give me the whole code in cpp for adding two numbers' }
            ],
            title: 'Add Two Numbers',
            description: 'Write a function to add two numbers',
            testCases: [],
            startCode: []
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies ? cookies.join('; ') : `token=${token}`
            },
            timeout: 25000
        });
        
        console.log('✅ AI Response received:');
        console.log('Status:', aiResponse.status);
        console.log('Response:', aiResponse.data.message);
        
    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
        if (error.response?.status === 503) {
            console.log('🔄 This is expected - Gemini servers are overloaded');
        }
    }
}

testAI();
