import { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import axiosClient from "../utils/axiosClient";
import { Send } from 'lucide-react';

function ChatAi({ problemData, currentCode, selectedLanguage, onClose }) {
    const [messages, setMessages] = useState([
        { role: 'ai', content: "Hi! I'm your AI coding assistant. I can help you with:\n\n• Understanding the problem\n• Suggesting algorithms and approaches\n• Writing complete solutions\n• Debugging your code\n• Explaining concepts\n\nWhat would you like help with?" }
    ]);
    const [isLoading, setIsLoading] = useState(false);

    const { register, handleSubmit, reset,formState: {errors} } = useForm();
    const messagesEndRef = useRef(null);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);

    const onSubmit = async (data) => {
        if (!data.message?.trim()) return;

        const userMessage = { role: 'user', content: data.message.trim() };
        setMessages(prev => [...prev, userMessage]);
        reset();
        setIsLoading(true);

        try {
            // Prepare messages in the format expected by the backend
            const apiMessages = [...messages, userMessage].map(msg => ({
                role: msg.role === 'ai' ? 'model' : 'user',
                content: msg.content
            }));

            const response = await axiosClient.post("/ai/chat", {
                messages: apiMessages,
                title: problemData?.title || 'Coding Problem',
                description: problemData?.description || 'No description available',
                testCases: problemData?.testCases || [],
                startCode: currentCode ? [{
                    language: selectedLanguage || 'javascript',
                    initialCode: currentCode
                }] : []
            }, {
                timeout: 20000 // 20 second timeout
            });

            if (response.data.success) {
                setMessages(prev => [...prev, {
                    role: 'ai',
                    content: response.data.message || response.data.response
                }]);
            } else {
                throw new Error(response.data.message || 'Unknown error');
            }
        } catch (error) {
            console.error("AI Chat Error:", error);

            let errorMessage = "Sorry, I encountered an issue. ";

            if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
                errorMessage = "The request is taking too long. Please try a shorter question or try again.";
            } else if (error.response?.status === 408) {
                errorMessage = "The AI is taking too long to respond. Please try again with a shorter question.";
            } else if (error.response?.status === 503) {
                errorMessage = "The AI service is busy right now. Please try again in a moment.";
            } else if (error.response?.status === 429) {
                errorMessage = "Too many requests. Please wait a moment before trying again.";
            } else if (error.response?.status === 401) {
                errorMessage = "Authentication issue. Please refresh the page and try again.";
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.message?.includes('Network Error')) {
                errorMessage = "Network connection issue. Please check your internet and try again.";
            } else {
                errorMessage = "Something went wrong. Please try again or refresh the page.";
            }

            setMessages(prev => [...prev, {
                role: 'ai',
                content: errorMessage
            }]);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="flex flex-col h-full">
            {/* Chat Header */}
            <div className="flex items-center justify-between p-4 border-b border-base-300">
                <h3 className="font-semibold">💬 AI Assistant</h3>
                {onClose && (
                    <button onClick={onClose} className="btn btn-sm btn-ghost">
                        ✕
                    </button>
                )}
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((msg, index) => (
                    <div
                        key={index}
                        className={`chat ${msg.role === "user" ? "chat-end" : "chat-start"}`}
                    >
                        <div className={`chat-bubble ${
                            msg.role === "user"
                                ? "chat-bubble-primary"
                                : "bg-base-200 text-base-content"
                        }`}>
                            {msg.content}
                        </div>
                    </div>
                ))}

                {/* Loading indicator */}
                {isLoading && (
                    <div className="chat chat-start">
                        <div className="chat-bubble bg-base-200 text-base-content">
                            <div className="flex items-center space-x-2">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                <span>AI is thinking...</span>
                            </div>
                        </div>
                    </div>
                )}

                <div ref={messagesEndRef} />
            </div>

            {/* Input Form */}
            <form
                onSubmit={handleSubmit(onSubmit)}
                className="p-4 border-t border-base-300"
            >
                <div className="flex items-center space-x-2">
                    <input
                        placeholder="Ask me about this problem..."
                        className="input input-bordered flex-1"
                        {...register("message", { required: true, minLength: 1 })}
                        disabled={isLoading}
                    />
                    <button
                        type="submit"
                        className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
                        disabled={isLoading || errors.message}
                    >
                        {!isLoading && <Send size={16} />}
                        Send
                    </button>
                </div>
                {errors.message && (
                    <p className="text-error text-sm mt-1">Please enter a message</p>
                )}
            </form>
        </div>
    );
}

export default ChatAi;