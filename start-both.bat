@echo off
echo ========================================
echo   CodeEditor Application Startup
echo ========================================
echo.

echo [1/4] Killing existing Node processes...
taskkill /f /im node.exe >nul 2>&1
echo     ✓ Processes cleared

echo.
echo [2/4] Starting Backend Server...
start "Backend Server" cmd /k "cd /d C:\codeeditor && echo Starting Backend... && node src\index.js"
echo     ✓ Backend starting on port 3003

echo.
echo [3/4] Waiting for backend to initialize...
timeout /t 5 /nobreak >nul
echo     ✓ Backend should be ready

echo.
echo [4/4] Starting Frontend...
start "Frontend" cmd /k "cd /d C:\codeeditor\frontend && echo Starting Frontend... && npm run dev"
echo     ✓ Frontend starting on port 5173

echo.
echo ========================================
echo   🚀 APPLICATION STARTED SUCCESSFULLY!
echo ========================================
echo.
echo Backend:  http://localhost:3003/health
echo Frontend: http://localhost:5173
echo.
echo ⚠️  If you see connection errors:
echo    1. Check both terminal windows are running
echo    2. Wait 10 seconds for full startup
echo    3. Refresh your browser
echo.
echo Press any key to close this window...
pause >nul
