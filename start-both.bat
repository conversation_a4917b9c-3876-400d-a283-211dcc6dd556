@echo off
echo ========================================
echo   CodeEditor Application Startup
echo ========================================
echo.

echo [1/5] Killing existing Node processes...
taskkill /f /im node.exe >nul 2>&1
echo     ✓ Processes cleared

echo.
echo [2/5] Clearing port conflicts...
for %%p in (3000,3001,3002,3003,5173,5174) do (
    netstat -ano | findstr :%%p >nul 2>&1
    if not errorlevel 1 (
        echo     Clearing port %%p...
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%%p') do taskkill /f /pid %%a >nul 2>&1
    )
)
echo     ✓ Ports cleared

echo.
echo [3/5] Waiting for system to stabilize...
timeout /t 3 /nobreak >nul
echo     ✓ System ready

echo.
echo [4/5] Starting Backend Server (Crash-Proof)...
start "Backend Server - KEEP OPEN" cmd /k "cd /d C:\codeeditor && echo ========== BACKEND SERVER ========== && echo Starting crash-proof backend server... && echo Database connections with retry logic enabled && echo Error recovery: ON && echo. && node src\index.js"
echo     ✓ Backend starting with auto-recovery

echo.
echo [5/5] Waiting for backend, then starting frontend...
timeout /t 8 /nobreak >nul
start "Frontend - KEEP OPEN" cmd /k "cd /d C:\codeeditor\frontend && echo ========== FRONTEND ========== && echo Starting frontend application... && echo Backend connection: http://localhost:3003 && echo. && npm run dev"
echo     ✓ Frontend starting

echo.
echo ========================================
echo   🚀 CRASH-PROOF STARTUP COMPLETE!
echo ========================================
echo.
echo Backend:  http://localhost:3003/health
echo Frontend: http://localhost:5173 or http://localhost:5174
echo.
echo ✅ FEATURES ENABLED:
echo    • Auto database reconnection
echo    • Error recovery (no crashes)
echo    • Port conflict resolution
echo    • Graceful restart handling
echo.
echo ⚠️  IMPORTANT:
echo    • Keep both terminal windows open
echo    • Login will work immediately after startup
echo    • If errors occur, server will auto-recover
echo.
echo Press any key to close this window...
pause >nul
