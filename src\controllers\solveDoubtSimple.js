const { GoogleGenerativeAI } = require("@google/generative-ai");

// Simple fallback responses for common problems
const fallbackResponses = {
    twoSum: {
        cpp: `**Approach:** Use a hash map to store numbers and their indices. For each number, check if its complement exists.

**Solution (C++):**
\`\`\`cpp
#include <vector>
#include <unordered_map>
using namespace std;

vector<int> twoSum(vector<int>& nums, int target) {
    unordered_map<int, int> map;
    
    for (int i = 0; i < nums.size(); i++) {
        int complement = target - nums[i];
        
        if (map.find(complement) != map.end()) {
            return {map[complement], i};
        }
        
        map[nums[i]] = i;
    }
    
    return {};
}
\`\`\`

**Explanation:** We iterate through the array once. For each element, we calculate the complement (target - current). If the complement exists in our hash map, we found our pair.

**Time Complexity:** O(n)
**Space Complexity:** O(n)`,
        
        java: `**Approach:** Use a HashMap to store numbers and their indices.

**Solution (Java):**
\`\`\`java
import java.util.*;

public int[] twoSum(int[] nums, int target) {
    Map<Integer, Integer> map = new HashMap<>();
    
    for (int i = 0; i < nums.length; i++) {
        int complement = target - nums[i];
        
        if (map.containsKey(complement)) {
            return new int[]{map.get(complement), i};
        }
        
        map.put(nums[i], i);
    }
    
    return new int[]{};
}
\`\`\`

**Time Complexity:** O(n)
**Space Complexity:** O(n)`,

        python: `**Approach:** Use a dictionary to store numbers and their indices.

**Solution (Python):**
\`\`\`python
def twoSum(nums, target):
    num_map = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        
        if complement in num_map:
            return [num_map[complement], i]
        
        num_map[num] = i
    
    return []
\`\`\`

**Time Complexity:** O(n)
**Space Complexity:** O(n)`
    },
    
    addNumbers: {
        cpp: `**Approach:** Simple addition using the + operator.

**Solution (C++):**
\`\`\`cpp
#include <iostream>
using namespace std;

int addTwoNumbers(int a, int b) {
    return a + b;
}

int main() {
    int num1, num2;
    cout << "Enter two numbers: ";
    cin >> num1 >> num2;
    
    int result = addTwoNumbers(num1, num2);
    cout << "Sum: " << result << endl;
    
    return 0;
}
\`\`\`

**Explanation:** This is a basic arithmetic operation that adds two integers.

**Time Complexity:** O(1)
**Space Complexity:** O(1)`,

        java: `**Approach:** Simple addition using the + operator.

**Solution (Java):**
\`\`\`java
public class Solution {
    public int addTwoNumbers(int a, int b) {
        return a + b;
    }
    
    public static void main(String[] args) {
        Solution sol = new Solution();
        int result = sol.addTwoNumbers(5, 3);
        System.out.println("Sum: " + result);
    }
}
\`\`\`

**Time Complexity:** O(1)
**Space Complexity:** O(1)`,

        python: `**Approach:** Simple addition using the + operator.

**Solution (Python):**
\`\`\`python
def add_two_numbers(a, b):
    return a + b

# Example usage
num1 = 5
num2 = 3
result = add_two_numbers(num1, num2)
print(f"Sum: {result}")
\`\`\`

**Time Complexity:** O(1)
**Space Complexity:** O(1)`
    }
};

function detectLanguage(userMessage) {
    if (userMessage.includes('cpp') || userMessage.includes('c++')) return 'cpp';
    if (userMessage.includes('java')) return 'java';
    if (userMessage.includes('python')) return 'python';
    return 'cpp'; // default
}

function detectProblemType(title, description) {
    const titleLower = (title || '').toLowerCase();
    const descLower = (description || '').toLowerCase();
    
    if (titleLower.includes('two sum') || descLower.includes('two sum')) {
        return 'twoSum';
    }
    
    if (titleLower.includes('add') && (titleLower.includes('number') || titleLower.includes('two'))) {
        return 'addNumbers';
    }
    
    return 'addNumbers'; // default
}

const solveDoubt = async (req, res) => {
    try {
        const { messages, title, description, testCases, startCode } = req.body;
        
        // Validate required fields
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Messages array is required and cannot be empty"
            });
        }

        // Check if this is a request for code
        const userMessage = messages[messages.length - 1]?.content?.toLowerCase() || '';
        const isCodeRequest = userMessage.includes('code') || userMessage.includes('solution') || 
                             userMessage.includes('implement') || userMessage.includes('cpp') || 
                             userMessage.includes('java') || userMessage.includes('python');

        // For now, always use fallback due to Gemini quota issues
        if (isCodeRequest) {
            const language = detectLanguage(userMessage);
            const problemType = detectProblemType(title, description);
            
            const response = fallbackResponses[problemType]?.[language] || 
                           fallbackResponses.addNumbers[language];
            
            const finalResponse = response + 
                "\n\n*Note: AI service is temporarily unavailable due to quota limits. This is a standard solution for this type of problem.*";
            
            return res.status(200).json({
                success: true,
                message: finalResponse,
                response: finalResponse,
                fallback: true
            });
        }

        // For non-code requests, provide helpful guidance
        const helpResponse = `I understand you're working on "${title || 'this coding problem'}". 

Here's how I can help you:

• **Ask for complete code**: Say "give me the whole code in cpp/java/python"
• **Get explanations**: Ask "explain the approach" or "how does this work?"
• **Debug code**: Share your code and ask "what's wrong with this?"
• **Learn concepts**: Ask about specific algorithms or data structures

What specific help would you like with this problem?

*Note: AI service is temporarily unavailable due to quota limits, but I can provide standard solutions for common problems.*`;

        res.status(200).json({
            success: true,
            message: helpResponse,
            response: helpResponse,
            fallback: true
        });

    } catch (err) {
        console.error('Error in solveDoubt:', err);
        
        res.status(500).json({
            success: false,
            message: "Something went wrong. Please try again or refresh the page.",
            error: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
    }
};

module.exports = solveDoubt;
