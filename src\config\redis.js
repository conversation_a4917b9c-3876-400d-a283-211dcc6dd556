const {createClient} = require('redis');

const client = createClient({
    username: 'default',
    password: process.env.REDIS_PASS,
    socket: {
        host: 'redis-11585.c305.ap-south-1-1.ec2.redns.redis-cloud.com',
        port: 11585,
        reconnectStrategy: (retries) => {
            console.log(`Redis reconnection attempt ${retries}`);
            if (retries > 10) {
                console.log('Redis: Max reconnection attempts reached, giving up');
                return false; // Stop reconnecting
            }
            return Math.min(retries * 100, 3000); // Exponential backoff, max 3 seconds
        }
    }
});

// Handle Redis connection errors gracefully
client.on('error', (err) => {
    console.error('Redis connection error:', err.message);
    // Don't crash the server, just log the error
});

client.on('connect', () => {
    console.log('✅ Redis connected successfully');
});

client.on('disconnect', () => {
    console.log('⚠️ Redis disconnected');
});

client.on('reconnecting', () => {
    console.log('🔄 Redis reconnecting...');
});

module.exports = client;