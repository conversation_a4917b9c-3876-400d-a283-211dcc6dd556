const { LANGUAGES, DIFFICULTY_LEVELS, PROBLEM_TAGS } = require('./constants');
const { ValidationError } = require('./errorHandler');

const validateRequired = (value, fieldName) => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    throw new ValidationError(`${fieldName} is required`, fieldName);
  }
};

const validateLanguage = (language) => {
  const validLanguages = Object.values(LANGUAGES);
  if (!validLanguages.includes(language)) {
    throw new ValidationError(
      `Invalid language. Must be one of: ${validLanguages.join(', ')}`,
      'language'
    );
  }
};

const validateDifficulty = (difficulty) => {
  const validDifficulties = Object.values(DIFFICULTY_LEVELS);
  if (!validDifficulties.includes(difficulty)) {
    throw new ValidationError(
      `Invalid difficulty. Must be one of: ${validDifficulties.join(', ')}`,
      'difficulty'
    );
  }
};

const validateTags = (tags) => {
  const validTags = Object.values(PROBLEM_TAGS);
  if (Array.isArray(tags)) {
    tags.forEach(tag => {
      if (!validTags.includes(tag)) {
        throw new ValidationError(
          `Invalid tag: ${tag}. Must be one of: ${validTags.join(', ')}`,
          'tags'
        );
      }
    });
  } else if (typeof tags === 'string') {
    if (!validTags.includes(tags)) {
      throw new ValidationError(
        `Invalid tag: ${tags}. Must be one of: ${validTags.join(', ')}`,
        'tags'
      );
    }
  } else {
    throw new ValidationError('Tags must be a string or array of strings', 'tags');
  }
};

const validateTestCases = (testCases, type = 'test cases') => {
  if (!Array.isArray(testCases) || testCases.length === 0) {
    throw new ValidationError(`At least one ${type} is required`, type);
  }

  testCases.forEach((testCase, index) => {
    if (!testCase.input || typeof testCase.input !== 'string') {
      throw new ValidationError(
        `${type}[${index}]: input is required and must be a string`,
        `${type}.${index}.input`
      );
    }
    if (!testCase.output || typeof testCase.output !== 'string') {
      throw new ValidationError(
        `${type}[${index}]: output is required and must be a string`,
        `${type}.${index}.output`
      );
    }
    if (!testCase.explanation || typeof testCase.explanation !== 'string') {
      throw new ValidationError(
        `${type}[${index}]: explanation is required and must be a string`,
        `${type}.${index}.explanation`
      );
    }
  });
};

const validateStartCode = (startCode) => {
  if (!Array.isArray(startCode) || startCode.length !== 3) {
    throw new ValidationError(
      'Start code must be an array with exactly 3 language entries',
      'startCode'
    );
  }

  const requiredLanguages = Object.values(LANGUAGES);
  const providedLanguages = startCode.map(sc => sc.language);

  requiredLanguages.forEach(lang => {
    if (!providedLanguages.includes(lang)) {
      throw new ValidationError(
        `Missing start code for language: ${lang}`,
        'startCode'
      );
    }
  });

  startCode.forEach((code, index) => {
    validateLanguage(code.language);
    if (!code.initialCode || typeof code.initialCode !== 'string') {
      throw new ValidationError(
        `startCode[${index}]: initialCode is required and must be a string`,
        `startCode.${index}.initialCode`
      );
    }
  });
};

const validateReferenceSolution = (referenceSolution) => {
  if (!Array.isArray(referenceSolution) || referenceSolution.length !== 3) {
    throw new ValidationError(
      'Reference solution must be an array with exactly 3 language entries',
      'referenceSolution'
    );
  }

  const requiredLanguages = Object.values(LANGUAGES);
  const providedLanguages = referenceSolution.map(rs => rs.language);

  requiredLanguages.forEach(lang => {
    if (!providedLanguages.includes(lang)) {
      throw new ValidationError(
        `Missing reference solution for language: ${lang}`,
        'referenceSolution'
      );
    }
  });

  referenceSolution.forEach((solution, index) => {
    validateLanguage(solution.language);
    if (!solution.completeCode || typeof solution.completeCode !== 'string') {
      throw new ValidationError(
        `referenceSolution[${index}]: completeCode is required and must be a string`,
        `referenceSolution.${index}.completeCode`
      );
    }
  });
};

const validateProblemData = (data) => {
  validateRequired(data.title, 'title');
  validateRequired(data.description, 'description');
  validateRequired(data.difficulty, 'difficulty');
  validateRequired(data.tags, 'tags');

  validateDifficulty(data.difficulty);
  validateTags(data.tags);
  validateTestCases(data.visibleTestCases, 'visible test cases');
  validateTestCases(data.hiddenTestCases, 'hidden test cases');
  validateStartCode(data.startCode);
  validateReferenceSolution(data.referenceSolution);
};

const validateSubmissionData = (data) => {
  validateRequired(data.code, 'code');
  validateRequired(data.language, 'language');
  validateLanguage(data.language);
};

module.exports = {
  validateRequired,
  validateLanguage,
  validateDifficulty,
  validateTags,
  validateTestCases,
  validateStartCode,
  validateReferenceSolution,
  validateProblemData,
  validateSubmissionData
};
