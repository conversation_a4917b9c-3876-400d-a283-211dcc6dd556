# 🎯 Working Binary Search Problem Example

## ✅ **CORRECT Problem Data for Admin Panel**

### **Basic Information:**
- **Title**: "Binary Search in Array"
- **Description**: "Given a sorted array and a target value, return the index of the target value. If not found, return -1."
- **Difficulty**: "easy"
- **Tags**: "array"

### **✅ CORRECT Visible Test Cases:**

**Test Case 1:**
```javascript
{
  input: "5\n1 3 5 7 9\n5",
  output: "2",
  explanation: "Array has 5 elements [1,3,5,7,9]. Target 5 is found at index 2."
}
```

**Test Case 2:**
```javascript
{
  input: "4\n2 4 6 8\n3", 
  output: "-1",
  explanation: "Array has 4 elements [2,4,6,8]. Target 3 is not found, return -1."
}
```

### **✅ CORRECT Hidden Test Cases:**

**Hidden Test Case 1:**
```javascript
{
  input: "3\n10 20 30\n20",
  output: "1"
}
```

**Hidden Test Case 2:**
```javascript
{
  input: "1\n42\n42",
  output: "0"
}
```

### **✅ CORRECT Start Code:**

**C++:**
```cpp
#include <iostream>
#include <vector>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    vector<int> arr(n);
    for(int i = 0; i < n; i++) {
        cin >> arr[i];
    }
    
    int target;
    cin >> target;
    
    // Your code here
    // Find target in arr and print the index
    // If not found, print -1
    
    return 0;
}
```

**Java:**
```java
import java.util.Scanner;

public class Solution {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        int n = sc.nextInt();
        int[] arr = new int[n];
        
        for(int i = 0; i < n; i++) {
            arr[i] = sc.nextInt();
        }
        
        int target = sc.nextInt();
        
        // Your code here
        // Find target in arr and print the index
        // If not found, print -1
        
        sc.close();
    }
}
```

**JavaScript:**
```javascript
const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

let input = [];
rl.on('line', (line) => {
    input.push(line);
});

rl.on('close', () => {
    const n = parseInt(input[0]);
    const arr = input[1].split(' ').map(Number);
    const target = parseInt(input[2]);
    
    // Your code here
    // Find target in arr and print the index
    // If not found, print -1
});
```

### **✅ CORRECT Reference Solutions:**

**C++:**
```cpp
#include <iostream>
#include <vector>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    vector<int> arr(n);
    for(int i = 0; i < n; i++) {
        cin >> arr[i];
    }
    
    int target;
    cin >> target;
    
    // Linear search (simple approach)
    for(int i = 0; i < n; i++) {
        if(arr[i] == target) {
            cout << i << endl;
            return 0;
        }
    }
    
    cout << -1 << endl;
    return 0;
}
```

**Java:**
```java
import java.util.Scanner;

public class Solution {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        int n = sc.nextInt();
        int[] arr = new int[n];
        
        for(int i = 0; i < n; i++) {
            arr[i] = sc.nextInt();
        }
        
        int target = sc.nextInt();
        
        // Linear search
        for(int i = 0; i < n; i++) {
            if(arr[i] == target) {
                System.out.println(i);
                sc.close();
                return;
            }
        }
        
        System.out.println(-1);
        sc.close();
    }
}
```

**JavaScript:**
```javascript
const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

let input = [];
rl.on('line', (line) => {
    input.push(line);
});

rl.on('close', () => {
    const n = parseInt(input[0]);
    const arr = input[1].split(' ').map(Number);
    const target = parseInt(input[2]);
    
    // Linear search
    for(let i = 0; i < n; i++) {
        if(arr[i] === target) {
            console.log(i);
            return;
        }
    }
    
    console.log(-1);
});
```

## 🧪 **Manual Verification:**

**Test with input: "5\n1 3 5 7 9\n5"**

1. Program reads n = 5
2. Program reads arr = [1, 3, 5, 7, 9]  
3. Program reads target = 5
4. Program finds 5 at index 2
5. Program outputs "2" ✅

**Test with input: "4\n2 4 6 8\n3"**

1. Program reads n = 4
2. Program reads arr = [2, 4, 6, 8]
3. Program reads target = 3  
4. Program doesn't find 3
5. Program outputs "-1" ✅

## 🎯 **Copy-Paste Ready Data:**

Use this exact format in your admin panel:

**Visible Test Case 1:**
- Input: `5\n1 3 5 7 9\n5`
- Output: `2`
- Explanation: `Array has 5 elements [1,3,5,7,9]. Target 5 is found at index 2.`

**Visible Test Case 2:**
- Input: `4\n2 4 6 8\n3`
- Output: `-1`  
- Explanation: `Array has 4 elements [2,4,6,8]. Target 3 is not found, return -1.`

**Hidden Test Case 1:**
- Input: `3\n10 20 30\n20`
- Output: `1`

**Hidden Test Case 2:**
- Input: `1\n42\n42`
- Output: `0`

This format will work correctly with Judge0 and your problem creation will succeed! 🎉
