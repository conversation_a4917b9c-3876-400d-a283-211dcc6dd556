#include <iostream>
#include <vector>
using namespace std;

int main() {
    cout << "Debug: Starting program" << endl;
    
    int n;
    cout << "Debug: Reading n..." << endl;
    cin >> n;
    cout << "Debug: n = " << n << endl;
    
    vector<int> arr(n);
    cout << "Debug: Reading array..." << endl;
    for(int i = 0; i < n; i++) {
        cin >> arr[i];
        cout << "Debug: arr[" << i << "] = " << arr[i] << endl;
    }
    
    int target;
    cout << "Debug: Reading target..." << endl;
    cin >> target;
    cout << "Debug: target = " << target << endl;
    
    // Search for target
    cout << "Debug: Searching for target..." << endl;
    for(int i = 0; i < n; i++) {
        if(arr[i] == target) {
            cout << "Debug: Found at index " << i << endl;
            cout << i << endl;  // This is the actual output
            return 0;
        }
    }
    
    cout << "Debug: Not found" << endl;
    cout << -1 << endl;  // This is the actual output
    return 0;
}
