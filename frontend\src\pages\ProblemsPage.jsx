import React, { useState, useEffect } from 'react';
import { Link } from 'react-router';
import axiosClient from '../utils/axiosClient';

const ProblemsPage = () => {
  const [problems, setProblems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [tagFilter, setTagFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    fetchProblems();
  }, []);

  const fetchProblems = async () => {
    try {
      setLoading(true);
      const response = await axiosClient.get('/problem/getAllProblem');
      console.log('📊 Problems received:', response.data);
      console.log('🏷️ Tags in problems:', response.data.map(p => ({ title: p.title, tags: p.tags })));
      setProblems(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching problems:', err);
      setError('Failed to fetch problems. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy': return 'badge-success';
      case 'medium': return 'badge-warning';
      case 'hard': return 'badge-error';
      default: return 'badge-ghost';
    }
  };

  const getTagDisplayName = (tag) => {
    const tagMap = {
      'array': 'Array',
      'linkedList': 'Linked List',
      'binarySearch': 'Binary Search',
      'sorting': 'Sorting',
      'twoPointers': 'Two Pointers',
      'slidingWindow': 'Sliding Window',
      'hashMap': 'Hash Map',
      'stack': 'Stack',
      'queue': 'Queue',
      'tree': 'Tree',
      'binaryTree': 'Binary Tree',
      'bst': 'BST',
      'heap': 'Heap',
      'greedy': 'Greedy',
      'backtracking': 'Backtracking',
      'dfs': 'DFS',
      'bfs': 'BFS',
      'recursion': 'Recursion',
      'math': 'Math',
      'string': 'String',
      'matrix': 'Matrix',
      'graph': 'Graph',
      'dp': 'Dynamic Programming'
    };
    return tagMap[tag] || tag;
  };

  const filteredProblems = problems.filter(problem => {
    const matchesSearch = problem.title?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDifficulty = difficultyFilter === 'all' || problem.difficulty === difficultyFilter;
    const matchesTag = tagFilter === 'all' || problem.tags === tagFilter;
    return matchesSearch && matchesDifficulty && matchesTag;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredProblems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProblems = filteredProblems.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, difficultyFilter, tagFilter, itemsPerPage]);

  const uniqueTags = [...new Set(problems.map(p => p.tags).filter(Boolean))];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="alert alert-error">
          <span>{error}</span>
          <button onClick={fetchProblems} className="btn btn-sm">Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">All Problems</h1>
        <div className="text-sm text-base-content/70">
          Showing {startIndex + 1}-{Math.min(endIndex, filteredProblems.length)} of {filteredProblems.length} problems
          {filteredProblems.length !== problems.length && ` (filtered from ${problems.length} total)`}
        </div>
      </div>

      {/* Filters */}
      <div className="card bg-base-100 shadow-lg p-4 mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Search */}
          <div className="form-control">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-base-content/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search problems by title..."
                className="input input-bordered w-64 pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Difficulty Filter */}
          <div className="form-control">
            <select
              className="select select-bordered"
              value={difficultyFilter}
              onChange={(e) => setDifficultyFilter(e.target.value)}
            >
              <option value="all">All Difficulties</option>
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
          </div>

          {/* Tag Filter */}
          <div className="form-control">
            <select
              className="select select-bordered"
              value={tagFilter}
              onChange={(e) => setTagFilter(e.target.value)}
            >
              <option value="all">All Tags</option>
              {uniqueTags.map(tag => (
                <option key={tag} value={tag}>{getTagDisplayName(tag)}</option>
              ))}
            </select>
          </div>

          {/* Items Per Page */}
          <div className="form-control">
            <select
              className="select select-bordered select-sm"
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>

          {/* Clear Filters */}
          {(searchTerm || difficultyFilter !== 'all' || tagFilter !== 'all') && (
            <button
              onClick={() => {
                setSearchTerm('');
                setDifficultyFilter('all');
                setTagFilter('all');
              setCurrentPage(1);
              }}
              className="btn btn-ghost btn-sm"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Problems List */}
      {currentProblems.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold mb-2">No problems found</h3>
          <p className="text-base-content/70">
            {problems.length === 0 
              ? "No problems have been created yet." 
              : "Try adjusting your search filters."}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {currentProblems.map((problem, index) => (
            <div key={problem._id} className="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-sm font-mono text-base-content/60">
                        #{startIndex + index + 1}
                      </span>
                      <h2 className="card-title text-lg">
                        <Link 
                          to={`/problem/${problem._id}`}
                          className="hover:text-primary transition-colors"
                        >
                          {problem.title}
                        </Link>
                      </h2>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className={`badge ${getDifficultyColor(problem.difficulty)}`}>
                        {problem.difficulty ? 
                          problem.difficulty.charAt(0).toUpperCase() + problem.difficulty.slice(1) : 
                          'Unknown'
                        }
                      </div>
                      
                      {problem.tags ? (
                        <div className="badge badge-outline">
                          {getTagDisplayName(problem.tags)}
                        </div>
                      ) : (
                        <div className="badge badge-ghost">
                          No Tag
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Link 
                      to={`/problem/${problem._id}`}
                      className="btn btn-primary btn-sm"
                    >
                      Solve
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="join">
                <button
                  className="join-item btn"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  «
                </button>

                {[...Array(totalPages)].map((_, index) => {
                  const page = index + 1;
                  // Show first page, last page, current page, and pages around current
                  const showPage = page === 1 ||
                                  page === totalPages ||
                                  Math.abs(page - currentPage) <= 1;

                  if (!showPage) {
                    // Show ellipsis for gaps
                    if (page === 2 && currentPage > 4) {
                      return <span key={page} className="join-item btn btn-disabled">...</span>;
                    }
                    if (page === totalPages - 1 && currentPage < totalPages - 3) {
                      return <span key={page} className="join-item btn btn-disabled">...</span>;
                    }
                    return null;
                  }

                  return (
                    <button
                      key={page}
                      className={`join-item btn ${currentPage === page ? 'btn-active' : ''}`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  );
                })}

                <button
                  className="join-item btn"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  »
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProblemsPage;
