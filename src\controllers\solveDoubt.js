const { GoogleGenerativeAI } = require("@google/generative-ai");

// Fallback code generation for common problems
function generateFallbackCodeResponse(title, description, userMessage) {
    const language = detectLanguage(userMessage);
    const problemType = detectProblemType(title, description);

    let response = `**Approach:** ${problemType.approach}\n\n**Solution (${language}):**\n\`\`\`${language}\n${problemType.code[language]}\n\`\`\`\n\n**Explanation:** ${problemType.explanation}\n\n**Time Complexity:** ${problemType.timeComplexity}\n**Space Complexity:** ${problemType.spaceComplexity}\n\n*Note: AI service is temporarily unavailable, so I've provided a standard solution. For more specific help, please try again later.*`;

    return response;
}

function detectLanguage(userMessage) {
    if (userMessage.includes('cpp') || userMessage.includes('c++')) return 'cpp';
    if (userMessage.includes('java')) return 'java';
    if (userMessage.includes('python')) return 'python';
    if (userMessage.includes('javascript') || userMessage.includes('js')) return 'javascript';
    return 'cpp'; // default
}

function detectProblemType(title, description) {
    const titleLower = (title || '').toLowerCase();
    const descLower = (description || '').toLowerCase();

    // Two Sum problem
    if (titleLower.includes('two sum') || descLower.includes('two sum')) {
        return {
            approach: "Use a hash map to store numbers and their indices. For each number, check if its complement (target - current) exists in the map.",
            explanation: "We iterate through the array once. For each element, we calculate what number we need to reach the target (complement = target - current). If this complement exists in our hash map, we found our pair. Otherwise, we store the current number and its index in the hash map.",
            timeComplexity: "O(n)",
            spaceComplexity: "O(n)",
            code: {
                cpp: `vector<int> twoSum(vector<int>& nums, int target) {
    unordered_map<int, int> map;

    for (int i = 0; i < nums.size(); i++) {
        int complement = target - nums[i];

        if (map.find(complement) != map.end()) {
            return {map[complement], i};
        }

        map[nums[i]] = i;
    }

    return {};
}`,
                java: `public int[] twoSum(int[] nums, int target) {
    Map<Integer, Integer> map = new HashMap<>();

    for (int i = 0; i < nums.length; i++) {
        int complement = target - nums[i];

        if (map.containsKey(complement)) {
            return new int[]{map.get(complement), i};
        }

        map.put(nums[i], i);
    }

    return new int[]{};
}`,
                python: `def twoSum(nums, target):
    num_map = {}

    for i, num in enumerate(nums):
        complement = target - num

        if complement in num_map:
            return [num_map[complement], i]

        num_map[num] = i

    return []`,
                javascript: `function twoSum(nums, target) {
    const map = new Map();

    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];

        if (map.has(complement)) {
            return [map.get(complement), i];
        }

        map.set(nums[i], i);
    }

    return [];
}`
            }
        };
    }

    // Add Two Numbers problem
    if (titleLower.includes('add') && (titleLower.includes('number') || titleLower.includes('two'))) {
        return {
            approach: "Simple addition of two numbers using the + operator.",
            explanation: "This is a basic arithmetic operation. We take two numbers as input and return their sum.",
            timeComplexity: "O(1)",
            spaceComplexity: "O(1)",
            code: {
                cpp: `int addTwoNumbers(int a, int b) {
    return a + b;
}

// For larger numbers or linked list representation:
// ListNode* addTwoNumbers(ListNode* l1, ListNode* l2) {
//     ListNode* dummy = new ListNode(0);
//     ListNode* current = dummy;
//     int carry = 0;
//
//     while (l1 || l2 || carry) {
//         int sum = carry;
//         if (l1) { sum += l1->val; l1 = l1->next; }
//         if (l2) { sum += l2->val; l2 = l2->next; }
//
//         carry = sum / 10;
//         current->next = new ListNode(sum % 10);
//         current = current->next;
//     }
//
//     return dummy->next;
// }`,
                java: `public int addTwoNumbers(int a, int b) {
    return a + b;
}`,
                python: `def add_two_numbers(a, b):
    return a + b`,
                javascript: `function addTwoNumbers(a, b) {
    return a + b;
}`
            }
        };
    }

    // Default generic problem
    return {
        approach: "Analyze the problem requirements and choose appropriate data structures and algorithms.",
        explanation: "Without specific problem details, I recommend breaking down the problem into smaller parts, identifying the input/output requirements, and considering time/space complexity constraints.",
        timeComplexity: "Depends on the algorithm chosen",
        spaceComplexity: "Depends on the data structures used",
        code: {
            cpp: `// Generic C++ solution template
#include <iostream>
#include <vector>
#include <unordered_map>
using namespace std;

int main() {
    // Read input
    // Process data
    // Output result
    return 0;
}`,
            java: `// Generic Java solution template
public class Solution {
    public void solve() {
        // Read input
        // Process data
        // Output result
    }
}`,
            python: `# Generic Python solution template
def solve():
    # Read input
    # Process data
    # Return result
    pass`,
            javascript: `// Generic JavaScript solution template
function solve() {
    // Read input
    // Process data
    // Return result
}`
        }
    };
}

const solveDoubt = async (req, res) => {
    try {
        const { messages, title, description, testCases, startCode } = req.body;

        // Validate required fields
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Messages array is required and cannot be empty"
            });
        }

        const genAI = new GoogleGenerativeAI(process.env.GEMINI_KEY);

        // Try Gemini 1.5 Pro first, fallback to Flash if needed
        let model;
        let modelName = "gemini-1.5-pro";

        model = genAI.getGenerativeModel({
            model: modelName,
            systemInstruction: `You are an expert Data Structures and Algorithms (DSA) coding assistant. You provide complete, working solutions and detailed explanations for coding problems.

## CURRENT PROBLEM CONTEXT:
[PROBLEM_TITLE]: ${title || 'Coding Problem'}
[PROBLEM_DESCRIPTION]: ${description || 'No description available'}
[TEST_CASES]: ${JSON.stringify(testCases || [])}
[STARTER_CODE]: ${JSON.stringify(startCode || [])}

## YOUR CAPABILITIES:
1. **Complete Solutions**: Provide full, working code solutions in the requested language
2. **Algorithm Explanation**: Explain the approach and algorithm used
3. **Code Analysis**: Review and debug user's code with corrections
4. **Multiple Approaches**: Show different ways to solve the same problem
5. **Optimization**: Suggest more efficient solutions with complexity analysis
6. **Concept Teaching**: Explain relevant DSA concepts with examples

## RESPONSE GUIDELINES:
- **Provide complete code** when asked for solutions
- **Explain the approach** before showing code
- **Include comments** in code for clarity
- **Show time/space complexity** for solutions
- **Give working examples** that pass the test cases
- **Be helpful and direct** - users want practical solutions

## RESPONSE FORMAT:
- Start with brief approach explanation
- Provide complete, commented code
- Explain key parts of the solution
- Include complexity analysis
- Suggest optimizations if applicable

## EXAMPLE RESPONSE STRUCTURE:
**Approach:** [Brief explanation of the algorithm]

**Solution:**
\`\`\`language
// Complete working code with comments
\`\`\`

**Explanation:** [How the code works]
**Time Complexity:** O(...)
**Space Complexity:** O(...)

Remember: Provide complete, working solutions that help users learn through practical examples.`
        });

        // Convert messages to the format expected by Gemini
        const formattedMessages = messages.map(msg => ({
            role: msg.role === 'user' ? 'user' : 'model',
            parts: [{ text: msg.content || msg.text || msg.message }]
        }));

        // Generate content using Gemini with timeout
        const result = await Promise.race([
            model.generateContent({
                contents: formattedMessages
            }),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Request timeout')), 15000)
            )
        ]);

        const response = result.response;
        const text = response.text();

        console.log('AI Response:', text);

        res.status(200).json({
            success: true,
            message: text,
            response: text
        });

    } catch (err) {
        console.error('Gemini AI Error:', err);

        // Check if this is a request for complete code solution
        const userMessage = messages[messages.length - 1]?.content?.toLowerCase() || '';
        const isCodeRequest = userMessage.includes('code') || userMessage.includes('solution') || userMessage.includes('implement') || userMessage.includes('cpp') || userMessage.includes('java') || userMessage.includes('python');

        let errorMessage = "I'm having trouble right now. ";
        let statusCode = 500;
        let fallbackResponse = null;

        if (err.message === 'Request timeout') {
            errorMessage = "The AI is taking too long to respond. Please try a shorter question or try again.";
            statusCode = 408;
        } else if (err.message?.includes('API_KEY') || err.status === 401) {
            errorMessage = "Authentication issue. Please refresh the page.";
            statusCode = 401;
        } else if (err.message?.includes('quota') || err.message?.includes('limit') || err.status === 429) {
            statusCode = 200; // Return success for fallback
            fallbackResponse = generateFallbackCodeResponse(title, description, userMessage);
        } else if (err.status === 400) {
            errorMessage = "Invalid request. Please try rephrasing your question.";
            statusCode = 400;
        } else if (err.status === 503 || err.message?.includes('overloaded') || err.message?.includes('Service Unavailable')) {
            statusCode = 200; // Return success for fallback
            fallbackResponse = generateFallbackCodeResponse(title, description, userMessage);
        } else if (err.message?.includes('fetch') || err.message?.includes('network')) {
            errorMessage = "Network connection issue. Please check your internet and try again.";
            statusCode = 502;
        } else {
            errorMessage = "Something went wrong. Please try again or refresh the page.";
        }

        if (fallbackResponse) {
            res.status(200).json({
                success: true,
                message: fallbackResponse,
                response: fallbackResponse,
                fallback: true
            });
        } else {
            res.status(statusCode).json({
                success: false,
                message: errorMessage,
                error: process.env.NODE_ENV === 'development' ? err.message : undefined
            });
        }
    }
};

module.exports = solveDoubt;
