const { GoogleGenerativeAI } = require("@google/generative-ai");

const solveDoubt = async (req, res) => {
    try {
        const { messages, title, description, testCases, startCode } = req.body;

        // Validate required fields
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Messages array is required and cannot be empty"
            });
        }

        const genAI = new GoogleGenerativeAI(process.env.GEMINI_KEY);
        const model = genAI.getGenerativeModel({
            model: "gemini-1.5-flash",
            systemInstruction: `You are an expert Data Structures and Algorithms (DSA) coding assistant. You provide complete, working solutions and detailed explanations for coding problems.

## CURRENT PROBLEM CONTEXT:
[PROBLEM_TITLE]: ${title || 'Coding Problem'}
[PROBLEM_DESCRIPTION]: ${description || 'No description available'}
[TEST_CASES]: ${JSON.stringify(testCases || [])}
[STARTER_CODE]: ${JSON.stringify(startCode || [])}

## YOUR CAPABILITIES:
1. **Complete Solutions**: Provide full, working code solutions in the requested language
2. **Algorithm Explanation**: Explain the approach and algorithm used
3. **Code Analysis**: Review and debug user's code with corrections
4. **Multiple Approaches**: Show different ways to solve the same problem
5. **Optimization**: Suggest more efficient solutions with complexity analysis
6. **Concept Teaching**: Explain relevant DSA concepts with examples

## RESPONSE GUIDELINES:
- **Provide complete code** when asked for solutions
- **Explain the approach** before showing code
- **Include comments** in code for clarity
- **Show time/space complexity** for solutions
- **Give working examples** that pass the test cases
- **Be helpful and direct** - users want practical solutions

## RESPONSE FORMAT:
- Start with brief approach explanation
- Provide complete, commented code
- Explain key parts of the solution
- Include complexity analysis
- Suggest optimizations if applicable

## EXAMPLE RESPONSE STRUCTURE:
**Approach:** [Brief explanation of the algorithm]

**Solution:**
\`\`\`language
// Complete working code with comments
\`\`\`

**Explanation:** [How the code works]
**Time Complexity:** O(...)
**Space Complexity:** O(...)

Remember: Provide complete, working solutions that help users learn through practical examples.`
        });

        // Convert messages to the format expected by Gemini
        const formattedMessages = messages.map(msg => ({
            role: msg.role === 'user' ? 'user' : 'model',
            parts: [{ text: msg.content || msg.text || msg.message }]
        }));

        // Generate content using Gemini with timeout
        const result = await Promise.race([
            model.generateContent({
                contents: formattedMessages
            }),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Request timeout')), 15000)
            )
        ]);

        const response = result.response;
        const text = response.text();

        console.log('AI Response:', text);

        res.status(200).json({
            success: true,
            message: text,
            response: text
        });

    } catch (err) {
        console.error('Gemini AI Error:', err);

        let errorMessage = "I'm having trouble right now. ";
        let statusCode = 500;

        if (err.message === 'Request timeout') {
            errorMessage = "The AI is taking too long to respond. Please try a shorter question or try again.";
            statusCode = 408;
        } else if (err.message?.includes('API_KEY') || err.status === 401) {
            errorMessage = "Authentication issue. Please refresh the page.";
            statusCode = 401;
        } else if (err.message?.includes('quota') || err.message?.includes('limit') || err.status === 429) {
            errorMessage = "Too many requests. Please wait a moment before trying again.";
            statusCode = 429;
        } else if (err.status === 400) {
            errorMessage = "Invalid request. Please try rephrasing your question.";
            statusCode = 400;
        } else if (err.status === 503 || err.message?.includes('overloaded')) {
            errorMessage = "The AI service is busy. Please try again in a moment.";
            statusCode = 503;
        } else if (err.message?.includes('fetch') || err.message?.includes('network')) {
            errorMessage = "Network connection issue. Please check your internet and try again.";
            statusCode = 502;
        } else {
            errorMessage = "Something went wrong. Please try again or refresh the page.";
        }

        res.status(statusCode).json({
            success: false,
            message: errorMessage,
            error: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
    }
};

module.exports = solveDoubt;
