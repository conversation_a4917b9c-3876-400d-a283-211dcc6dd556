const { GoogleGenerativeAI } = require("@google/generative-ai");

const solveDoubt = async (req, res) => {
    try {
        const { messages, title, description, testCases, startCode } = req.body;

        // Validate required fields
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Messages array is required and cannot be empty"
            });
        }

        const genAI = new GoogleGenerativeAI(process.env.GEMINI_KEY);
        const model = genAI.getGenerativeModel({
            model: "gemini-1.5-flash",
            systemInstruction: `You are an expert Data Structures and Algorithms (DSA) tutor specializing in helping users solve coding problems. Your role is strictly limited to DSA-related assistance only.

## CURRENT PROBLEM CONTEXT:
[PROBLEM_TITLE]: ${title || 'Coding Problem'}
[PROBLEM_DESCRIPTION]: ${description || 'No description available'}
[TEST_CASES]: ${JSON.stringify(testCases || [])}
[STARTER_CODE]: ${JSON.stringify(startCode || [])}

## YOUR RESPONSIBILITIES:
1. **Problem Analysis**: Help break down the problem into smaller, manageable parts
2. **Algorithm Guidance**: Suggest appropriate algorithms, data structures, and approaches
3. **Code Review**: Analyze user's code for correctness, efficiency, and best practices
4. **Debugging Help**: Assist in identifying and fixing logical errors
5. **Optimization Tips**: Suggest improvements for time/space complexity
6. **Concept Explanation**: Explain relevant DSA concepts clearly with examples
7. **Hint Provision**: Give progressive hints without revealing the complete solution immediately

## INTERACTION GUIDELINES:
- **Be Educational**: Focus on teaching concepts rather than just providing answers
- **Be Encouraging**: Maintain a supportive and motivating tone
- **Be Specific**: Reference the current problem context in your responses
- **Be Progressive**: Start with hints and gradually provide more detailed guidance if needed
- **Be Practical**: Provide code examples when helpful, but encourage user to implement

## RESPONSE FORMAT:
- Keep responses concise but comprehensive
- Use code blocks for code examples
- Use bullet points for step-by-step guidance
- Include time/space complexity analysis when relevant

## RESTRICTIONS:
- **ONLY** respond to DSA and coding-related queries
- **DO NOT** help with non-programming topics
- **DO NOT** provide complete solutions immediately - guide the user to discover them
- **DO NOT** engage in general conversation unrelated to the current coding problem

Remember: Your goal is to help users learn and understand DSA concepts through the lens of the current problem, not just to provide quick answers.`
        });

        // Convert messages to the format expected by Gemini
        const formattedMessages = messages.map(msg => ({
            role: msg.role === 'user' ? 'user' : 'model',
            parts: [{ text: msg.content || msg.text || msg.message }]
        }));

        // Generate content using Gemini
        const result = await model.generateContent({
            contents: formattedMessages
        });

        const response = result.response;
        const text = response.text();

        console.log('AI Response:', text);

        res.status(200).json({
            success: true,
            message: text,
            response: text
        });

    } catch (err) {
        console.error('Gemini AI Error:', err);

        let errorMessage = "Internal server error";
        let statusCode = 500;

        if (err.message?.includes('API_KEY') || err.status === 401) {
            errorMessage = "Invalid API key";
            statusCode = 401;
        } else if (err.message?.includes('quota') || err.message?.includes('limit') || err.status === 429) {
            errorMessage = "API quota exceeded. Please try again later.";
            statusCode = 429;
        } else if (err.status === 400) {
            errorMessage = "Invalid request format";
            statusCode = 400;
        } else if (err.status === 503 || err.message?.includes('overloaded')) {
            errorMessage = "AI service is temporarily overloaded. Please try again in a few moments.";
            statusCode = 503;
        } else if (err.message?.includes('fetch') || err.message?.includes('network')) {
            errorMessage = "Network error connecting to AI service";
            statusCode = 502;
        }

        res.status(statusCode).json({
            success: false,
            message: errorMessage,
            error: process.env.NODE_ENV === 'development' ? err.message : undefined,
            retryAfter: statusCode === 503 ? 30 : undefined
        });
    }
};

module.exports = solveDoubt;
