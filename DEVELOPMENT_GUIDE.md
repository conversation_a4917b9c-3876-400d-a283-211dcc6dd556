# Development Guide

## Quick Start

### 1. Setup Development Environment
```bash
npm run setup
```
This will:
- ✅ Check environment variables
- ✅ Verify dependencies
- ✅ Kill any processes on port 3000
- ✅ Prepare development environment

### 2. Start Development Server
```bash
npm run dev
```
This starts the server with nodemon for auto-restart on file changes.

### 3. Alternative Commands
```bash
npm run start          # Production server
npm run kill-port      # Kill processes on port 3000
npm run clean-start    # Kill port + start dev server
```

## Server Features

### 🚀 **Smart Port Handling**
The server now automatically handles port conflicts:
- If port 3000 is busy, it tries 3001, 3002, etc.
- No more manual port killing needed
- Clear logging shows which port is being used

### 📊 **Health Check Endpoint**
```bash
curl http://localhost:3000/health
```
Returns:
```json
{
  "status": "OK",
  "timestamp": "2025-07-09T12:55:25.386Z",
  "uptime": 21.33
}
```

### 🎯 **Structured Logging**
All logs are now properly formatted with timestamps and colors:
```
[2025-07-09T12:55:05.467Z] SUCCESS: Server running on port 3000
[2025-07-09T12:55:05.467Z] INFO: Health check available at http://localhost:3000/health
```

### 🛡️ **Comprehensive Error Handling**
- Custom error classes for different error types
- Proper HTTP status codes
- Detailed error messages for debugging
- Production-safe error responses

## Code Structure

### 📁 **New Utility Files**
```
src/utils/
├── constants.js      # All constants and enums
├── errorHandler.js   # Error classes and global handler
├── validation.js     # Input validation functions
├── logger.js         # Structured logging
└── problemUtility.js # Improved Judge0 integration
```

### 🔧 **Development Scripts**
```
scripts/
└── dev-setup.js     # Development environment setup
```

## API Improvements

### ✅ **Better Error Responses**
**Before:**
```json
"Internal Server Error"
```

**After:**
```json
{
  "success": false,
  "error": "Code is required and must be a non-empty string",
  "field": "code"
}
```

### ✅ **Consistent Response Format**
All API responses now follow a consistent structure:
```json
{
  "success": true/false,
  "data": {...},        // On success
  "error": "message",   // On error
  "field": "fieldName"  // For validation errors
}
```

### ✅ **Request Logging**
Every API request is logged with:
- Method and URL
- Response time
- Status code
- User IP

## Frontend Fixes

### 🔧 **Language Mapping Fixed**
- Frontend now uses correct language format (`c++`, `java`, `javascript`)
- No more "Cannot read properties of undefined" errors
- Proper error handling in React components

### 🔧 **Submission History Fixed**
- Handles cases where backend returns string instead of array
- Safe array operations with proper validation
- Better error messages

## Environment Variables

Required variables in `.env`:
```env
PORT=3000
DB_CONNECT_STRING="mongodb://..."
JWT_KEY="your-jwt-secret"
REDIS_PASS="your-redis-password"
JUDGE0_KEY="your-judge0-api-key"
SKIP_REFERENCE_VALIDATION=true  # For development
```

## Development Workflow

### 🔄 **Daily Development**
1. `npm run setup` - Setup environment (first time or after issues)
2. `npm run dev` - Start development server
3. Make changes - Server auto-restarts
4. Check logs for any issues

### 🐛 **Debugging**
1. Check server logs for detailed error information
2. Use health endpoint to verify server status
3. Check browser console for frontend errors
4. Use structured logging data for troubleshooting

### 🚀 **Production Deployment**
1. Set `NODE_ENV=production`
2. Set `SKIP_REFERENCE_VALIDATION=false`
3. Use `npm run start` instead of `npm run dev`
4. Monitor health endpoint for uptime

## Common Issues & Solutions

### ❌ **Port Already in Use**
**Solution:** Run `npm run setup` or `npm run kill-port`

### ❌ **Database Connection Failed**
**Solution:** Check `DB_CONNECT_STRING` in `.env`

### ❌ **Judge0 API Errors**
**Solution:** Verify `JUDGE0_KEY` in `.env`

### ❌ **Frontend Language Errors**
**Solution:** Ensure language format matches backend (`c++`, not `C++`)

## Next Steps

### 🎯 **Recommended Improvements**
1. **Add Unit Tests**
   ```bash
   npm install --save-dev jest supertest
   ```

2. **Add API Documentation**
   ```bash
   npm install swagger-ui-express swagger-jsdoc
   ```

3. **Add Rate Limiting**
   ```bash
   npm install express-rate-limit
   ```

4. **Add Caching**
   ```bash
   # Redis caching for frequently accessed data
   ```

### 📚 **Learning Resources**
- [Express.js Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)
- [Node.js Error Handling](https://nodejs.org/api/errors.html)
- [MongoDB Best Practices](https://docs.mongodb.com/manual/administration/production-notes/)

## Support

If you encounter any issues:
1. Check the logs for detailed error information
2. Run `npm run setup` to reset the development environment
3. Verify all environment variables are set correctly
4. Check the health endpoint to ensure server is running

The codebase is now much more maintainable and follows industry best practices! 🎉
