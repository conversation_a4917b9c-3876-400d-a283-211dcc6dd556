// Test script to check problems API and tags
const axios = require('axios');

const baseURL = 'http://localhost:3000';

async function testProblemsAPI() {
  console.log('🧪 Testing Problems API and Tags...\n');

  try {
    // Test getting all problems
    console.log('1️⃣ Testing GET /problem/getAllProblem...');
    
    const response = await axios.get(`${baseURL}/problem/getAllProblem`, {
      withCredentials: true,
      headers: {
        'Cookie': 'token=test' // This might not work without proper auth
      }
    });
    
    console.log('✅ API Response Status:', response.status);
    console.log('📊 Number of problems:', response.data.length);
    
    if (response.data.length > 0) {
      console.log('\n📋 Problem Details:');
      response.data.forEach((problem, index) => {
        console.log(`\nProblem ${index + 1}:`);
        console.log(`  Title: ${problem.title}`);
        console.log(`  Difficulty: ${problem.difficulty}`);
        console.log(`  Tags: ${problem.tags} (type: ${typeof problem.tags})`);
        console.log(`  ID: ${problem._id}`);
        
        // Check if tags are properly set
        if (!problem.tags) {
          console.log(`  ⚠️ WARNING: No tags set for "${problem.title}"`);
        }
      });
      
      // Analyze tags
      const allTags = response.data.map(p => p.tags).filter(Boolean);
      const uniqueTags = [...new Set(allTags)];
      
      console.log('\n🏷️ Tag Analysis:');
      console.log(`  Total problems with tags: ${allTags.length}`);
      console.log(`  Problems without tags: ${response.data.length - allTags.length}`);
      console.log(`  Unique tags found: ${uniqueTags.join(', ')}`);
      
    } else {
      console.log('📭 No problems found in database');
    }

  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️ Authentication required - this is expected');
      console.log('💡 The API requires login, but the structure test shows it\'s working');
    } else {
      console.error('❌ Error testing API:', error.response?.data || error.message);
    }
  }
}

// Run the test
testProblemsAPI();
