{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../lib/cluster/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,kBAAkB,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC1D,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,kDAAkD,CAAC,WAAW,EAAE,aAAa,EAAiB,cAAc,EAAE,YAAY,EAAe,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAC9Q,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAE3C,OAAO,iBAAiB,EAAE,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC/E,OAAiC,EAAE,4BAA4B,EAAE,MAAM,iBAAiB,CAAC;AACzF,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,qBAAqB,EAAE,6BAA6B,EAAE,MAAM,iBAAiB,CAAC;AAIvF,UAAU,gBAAgB,CACxB,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAEhC,SAAQ,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACtC,cAAc,CAAC,EAAE,qBAAqB,CAAC,YAAY,CAAe,CAAC;CACpE;AAED,MAAM,MAAM,yBAAyB,GAAG,IAAI,CAC1C,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,CAAC,EAChH,MAAM,gBAAgB,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAsB,CACnH,CAAC;AAEF,MAAM,WAAW,mBAAmB,CAClC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,CAAC,SAAS,cAAc,GAAG,cAAc,EACzC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,IAAI,SAAS,YAAY,GAAG,YAAY,EACxC,YAAY,SAAS,WAAW,GAAG,WAAW,CAE9C,SAAQ,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe;IACnE;;;OAGG;IACH,SAAS,EAAE,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC5C;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;IAC9C;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;OAEG;IAEH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC;;;OAGG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,eAAe,CAAC,EAAE,6BAA6B,GAAG,qBAAqB,CAAC;CACzE;AAGD,KAAK,cAAc,CACjB,IAAI,SAAS,WAAW,EACxB,OAAO,SAAS,OAAO,IACrB,OAAO,CAAC,mBAAmB,CAAC,SAAS,IAAI,GAAG,CAC9C,OAAO,CAAC,oBAAoB,CAAC,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK,CAC1D,GAAG,IAAI,CAAC;AAGT,KAAK,YAAY,CACf,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,OAAO,QAAQ,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CACpI,CAAC;AAEF,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KAC/F;CACF,CAAC;AAEF,KAAK,aAAa,CAChB,CAAC,SAAS,cAAc,EACxB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KAC/F;CACF,CAAC;AAEF,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CACtF,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAC1B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IAEnC,CACF,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe,GACvD,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,GAChC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACpC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnC,CAAC;AAEF,MAAM,WAAW,qBAAqB,CACpC,YAAY,SAAS,WAAW,GAAG,WAAW,CAE9C,SAAQ,cAAc,CAAC,YAAY,CAAC;CAErC;AAMD,MAAM,CAAC,OAAO,OAAO,YAAY,CAC/B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAEhC,SAAQ,YAAY;;IAuEpB,MAAM,CAAC,OAAO,CACZ,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EAErC,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe,cAkBlD,KAAK,mBAAmB,EAAE,MAAM,QAAQ,aAAa,EAAE,SAAS,CAAC,CAAC;IAMtF,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EAErC,OAAO,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe;IAI1E,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe,CAAC;IAElF,QAAQ,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAEhE,OAAO,CAAC,KAAK,CAAQ;IACrB,OAAO,CAAC,eAAe,CAAC,CAAoD;IAE5E;;;OAGG;IACH,IAAI,KAAK,mEAER;IAED,IAAI,eAAe,8CAElB;IAED;;;OAGG;IACH,IAAI,OAAO,wEAEV;IAED;;;OAGG;IACH,IAAI,QAAQ,6CAEX;IAED;;;OAGG;IACH,IAAI,aAAa,4HAEhB;IAED;;OAEG;IACH,IAAI,UAAU,sLAEb;IAED,IAAI,MAAM,YAET;gBAEW,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAe;IAWnF,SAAS,CACP,EAAE,SAAS,YAAY,GAAG,CAAC,EAC3B,EAAE,SAAS,cAAc,GAAG,CAAC,EAC7B,EAAE,SAAS,YAAY,GAAG,CAAC,EAC3B,KAAK,SAAS,YAAY,GAAG,IAAI,EACjC,aAAa,SAAS,WAAW,GAAG,YAAY,EAChD,SAAS,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;IAQtE,OAAO;IAKb,kBAAkB,CAChB,OAAO,SAAS,qBAAqB,CAAC,YAAY,CAAsB,EACxE,YAAY,SAAS,WAAW,EAEhC,OAAO,EAAE,OAAO;IAalB,OAAO,CAAC,oBAAoB;IAoB5B;;OAEG;IACH,eAAe,CAAC,YAAY,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY;IAY3E,UAAU,CAAC,CAAC,EACV,EAAE,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,EAAE,qBAAqB,KAAK,OAAO,CAAC,CAAC,CAAC,YAEhF,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,YAAY,qBAAqB;IAkB/F,QAAQ,CAAC,CAAC,EACd,QAAQ,EAAE,aAAa,GAAG,SAAS,EACnC,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,OAAO,EAAE,qBAAqB,GAAG,SAAS,EAC1C,EAAE,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,EAAE,qBAAqB,KAAK,OAAO,CAAC,CAAC,CAAC,GACrG,OAAO,CAAC,CAAC,CAAC;IA8CP,WAAW,CAAC,CAAC,GAAG,UAAU,EAC9B,QAAQ,EAAE,aAAa,GAAG,SAAS,EACnC,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,IAAI,EAAE,gBAAgB,EACtB,OAAO,CAAC,EAAE,qBAAqB,GAE9B,OAAO,CAAC,CAAC,CAAC;IASb,KAAK,CAAC,OAAO,CAAC,EAAE,aAAa;IAgB7B,KAAK,aAhBW,aAAa,mEAgBV;IAEb,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACvC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAMhB,SAAS,wCARG,MAAM,GAAG,MAAM,MAAM,CAAC,4EAQP;IAErB,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACzC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,EAClC,UAAU,CAAC,EAAE,CAAC;IAOhB,WAAW,yCATE,MAAM,GAAG,MAAM,MAAM,CAAC,aACtB,eAAe,OAAO,CAAC,+CAQL;IAEzB,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACxC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAMhB,UAAU,wCARE,MAAM,GAAG,MAAM,MAAM,CAAC,4EAQL;IAEvB,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EAC1C,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC;IAOhB,YAAY,yCATC,MAAM,GAAG,MAAM,MAAM,CAAC,yFASF;IAE3B,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACxC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAwBhB,UAAU,wCA1BE,MAAM,GAAG,MAAM,MAAM,CAAC,4EA0BL;IAE7B,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACpC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC;IAQhB,YAAY,wCAVA,MAAM,GAAG,MAAM,MAAM,CAAC,yFAUD;IAEjC;;OAEG;IACH,IAAI;IAIJ;;OAEG;IACH,UAAU;IAIV,KAAK;IAKL,OAAO;IAKP,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAIvD;;;OAGG;IACH,aAAa;IAIb;;;OAGG;IACH,iBAAiB,CAAC,IAAI,EAAE,MAAM;IAI9B;;;OAGG;IACH,UAAU;IAIV;;;OAGG;IACH,aAAa,CAAC,IAAI,EAAE,MAAM;CAG3B"}