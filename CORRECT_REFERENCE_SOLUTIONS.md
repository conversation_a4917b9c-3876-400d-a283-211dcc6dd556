# ✅ CORRECT Reference Solutions for Binary Search Problem

## Test Case Format:
```
Input: "5\n1 3 5 7 9\n5"
Output: "2"
```

## ✅ C++ Reference Solution:
```cpp
#include <iostream>
#include <vector>
using namespace std;

int main() {
    int n;
    cin >> n;
    
    vector<int> arr(n);
    for(int i = 0; i < n; i++) {
        cin >> arr[i];
    }
    
    int target;
    cin >> target;
    
    for(int i = 0; i < n; i++) {
        if(arr[i] == target) {
            cout << i << endl;
            return 0;
        }
    }
    
    cout << -1 << endl;
    return 0;
}
```

## ✅ Java Reference Solution:
```java
import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        int n = sc.nextInt();
        int[] arr = new int[n];
        
        for(int i = 0; i < n; i++) {
            arr[i] = sc.nextInt();
        }
        
        int target = sc.nextInt();
        
        for(int i = 0; i < n; i++) {
            if(arr[i] == target) {
                System.out.println(i);
                sc.close();
                return;
            }
        }
        
        System.out.println(-1);
        sc.close();
    }
}
```

## ✅ JavaScript Reference Solution:
```javascript
const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

let input = [];
rl.on('line', (line) => {
    input.push(line.trim());
});

rl.on('close', () => {
    const n = parseInt(input[0]);
    const arr = input[1].split(' ').map(Number);
    const target = parseInt(input[2]);
    
    for(let i = 0; i < n; i++) {
        if(arr[i] === target) {
            console.log(i);
            return;
        }
    }
    
    console.log(-1);
});
```

## 🚨 CRITICAL RULES:

1. **NO DEBUG OUTPUT** - Only output the final result
2. **NO EXTRA NEWLINES** - Don't add extra `\n` characters
3. **EXACT OUTPUT MATCH** - Output must match exactly what you specify
4. **CLEAN INPUT READING** - Read input exactly as specified

## 🧪 Manual Test:

**Input:** `5\n1 3 5 7 9\n5`
**Expected Output:** `2`

**Step by step:**
1. Read n = 5
2. Read array = [1, 3, 5, 7, 9]
3. Read target = 5
4. Find 5 at index 2
5. Output: 2 ✅

## ❌ Common Mistakes:

### Mistake 1: Debug Output
```cpp
cout << "Found at index: " << i << endl;  // ❌ WRONG
```
Should be:
```cpp
cout << i << endl;  // ✅ CORRECT
```

### Mistake 2: Extra Formatting
```cpp
cout << "Result: " << i << "\n";  // ❌ WRONG
```
Should be:
```cpp
cout << i << endl;  // ✅ CORRECT
```

### Mistake 3: Wrong Input Reading
```cpp
// ❌ WRONG - trying to read formatted input
string input;
getline(cin, input);
// parse "arr = [1,3,5,7,9] key = 5"
```
Should be:
```cpp
// ✅ CORRECT - read actual stdin
int n;
cin >> n;
// read numbers directly
```

## 🎯 Copy-Paste Ready Solutions:

Use these exact solutions in your admin panel. They are tested and work correctly with the input format `"5\n1 3 5 7 9\n5"` and produce output `"2"`.
