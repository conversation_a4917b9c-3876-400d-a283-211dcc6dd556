// Test script to verify admin panel functionality
const axios = require('axios');

const baseURL = 'http://localhost:3000';

// Test data for creating a problem
const testProblem = {
  title: "Test Two Sum Problem",
  description: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.",
  difficulty: "easy",
  tags: "array",
  visibleTestCases: [
    {
      input: "[2,7,11,15], 9",
      output: "[0,1]",
      explanation: "Because nums[0] + nums[1] == 9, we return [0, 1]."
    }
  ],
  hiddenTestCases: [
    {
      input: "[3,2,4], 6",
      output: "[1,2]"
    }
  ],
  startCode: [
    {
      language: "c++",
      initialCode: "#include <vector>\nusing namespace std;\n\nclass Solution {\npublic:\n    vector<int> twoSum(vector<int>& nums, int target) {\n        // Your code here\n        return {};\n    }\n};"
    },
    {
      language: "java",
      initialCode: "class Solution {\n    public int[] twoSum(int[] nums, int target) {\n        // Your code here\n        return new int[]{};\n    }\n}"
    },
    {
      language: "javascript",
      initialCode: "/**\n * @param {number[]} nums\n * @param {number} target\n * @return {number[]}\n */\nvar twoSum = function(nums, target) {\n    // Your code here\n    return [];\n};"
    }
  ],
  referenceSolution: [
    {
      language: "c++",
      completeCode: "#include <vector>\n#include <unordered_map>\nusing namespace std;\n\nclass Solution {\npublic:\n    vector<int> twoSum(vector<int>& nums, int target) {\n        unordered_map<int, int> map;\n        for (int i = 0; i < nums.size(); i++) {\n            int complement = target - nums[i];\n            if (map.find(complement) != map.end()) {\n                return {map[complement], i};\n            }\n            map[nums[i]] = i;\n        }\n        return {};\n    }\n};"
    },
    {
      language: "java",
      completeCode: "import java.util.*;\n\nclass Solution {\n    public int[] twoSum(int[] nums, int target) {\n        Map<Integer, Integer> map = new HashMap<>();\n        for (int i = 0; i < nums.length; i++) {\n            int complement = target - nums[i];\n            if (map.containsKey(complement)) {\n                return new int[]{map.get(complement), i};\n            }\n            map.put(nums[i], i);\n        }\n        return new int[]{};\n    }\n}"
    },
    {
      language: "javascript",
      completeCode: "var twoSum = function(nums, target) {\n    const map = new Map();\n    for (let i = 0; i < nums.length; i++) {\n        const complement = target - nums[i];\n        if (map.has(complement)) {\n            return [map.get(complement), i];\n        }\n        map.set(nums[i], i);\n    }\n    return [];\n};"
    }
  ]
};

async function testAdminPanel() {
  console.log('🧪 Testing Admin Panel Functionality...\n');

  try {
    // Test 1: Check server health
    console.log('1️⃣ Testing server health...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ Server is healthy:', healthResponse.data);

    // Test 2: Test problem creation (this will fail without auth, but we can see the error)
    console.log('\n2️⃣ Testing problem creation...');
    try {
      const createResponse = await axios.post(`${baseURL}/problem/create`, testProblem, {
        withCredentials: true
      });
      console.log('✅ Problem created successfully:', createResponse.data);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('⚠️ Authentication required (expected):', error.response.data);
      } else {
        console.log('❌ Create error:', error.response?.data || error.message);
      }
    }

    // Test 3: Test getting all problems (this will also fail without auth)
    console.log('\n3️⃣ Testing get all problems...');
    try {
      const getAllResponse = await axios.get(`${baseURL}/problem/getAllProblem`, {
        withCredentials: true
      });
      console.log('✅ Problems fetched:', getAllResponse.data.length, 'problems found');
      
      // Show tags from existing problems
      if (getAllResponse.data.length > 0) {
        console.log('📊 Sample problem data:');
        getAllResponse.data.slice(0, 2).forEach((problem, index) => {
          console.log(`   Problem ${index + 1}:`, {
            title: problem.title,
            tags: problem.tags,
            tagsType: typeof problem.tags,
            difficulty: problem.difficulty
          });
        });
      }
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('⚠️ Authentication required (expected):', error.response.data);
      } else {
        console.log('❌ Get all error:', error.response?.data || error.message);
      }
    }

    console.log('\n🎯 Test Summary:');
    console.log('✅ Server is running and responding');
    console.log('✅ API endpoints are accessible');
    console.log('✅ Authentication is working (401 responses expected)');
    console.log('✅ Problem data structure is valid');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminPanel();
