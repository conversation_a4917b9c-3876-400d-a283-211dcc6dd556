const { spawn } = require('child_process');
const path = require('path');

let serverProcess = null;
let restartCount = 0;
const maxRestarts = 10;

function startServer() {
    console.log(`🚀 Starting server (attempt ${restartCount + 1}/${maxRestarts})...`);
    
    serverProcess = spawn('node', ['src/index.js'], {
        cwd: __dirname,
        stdio: 'inherit'
    });

    serverProcess.on('close', (code) => {
        console.log(`\n💥 Server process exited with code ${code}`);
        
        if (code !== 0 && restartCount < maxRestarts) {
            restartCount++;
            console.log(`🔄 Restarting server in 3 seconds... (${restartCount}/${maxRestarts})`);
            setTimeout(startServer, 3000);
        } else if (restartCount >= maxRestarts) {
            console.log(`❌ Maximum restart attempts (${maxRestarts}) reached. Please check the logs.`);
            process.exit(1);
        } else {
            console.log('✅ Server shut down gracefully');
            process.exit(0);
        }
    });

    serverProcess.on('error', (error) => {
        console.error(`❌ Failed to start server: ${error.message}`);
        if (restartCount < maxRestarts) {
            restartCount++;
            console.log(`🔄 Retrying in 3 seconds... (${restartCount}/${maxRestarts})`);
            setTimeout(startServer, 3000);
        }
    });
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
    if (serverProcess) {
        serverProcess.kill('SIGINT');
    }
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM. Shutting down gracefully...');
    if (serverProcess) {
        serverProcess.kill('SIGTERM');
    }
});

console.log('🎯 Server Auto-Restart Manager Started');
console.log('📝 This will automatically restart the server if it crashes');
console.log('🔄 Maximum restarts: ' + maxRestarts);
console.log('⏱️  Restart delay: 3 seconds');
console.log('🛑 Press Ctrl+C to stop\n');

startServer();
